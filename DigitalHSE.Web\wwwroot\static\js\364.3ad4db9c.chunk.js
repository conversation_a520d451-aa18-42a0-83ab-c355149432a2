"use strict";(self.webpackChunkdigitalhse_web=self.webpackChunkdigitalhse_web||[]).push([[364],{13019:(e,r,a)=>{a.d(r,{V:()=>n});var s=a(3035),l=a(9950),t=a(11942),c=a.n(t),d=a(69344),n=(0,l.forwardRef)((function(e,r){var a=e.children,t=e.as,c=void 0===t?"div":t,n=e.className,o=(0,s.Tt)(e,["children","as","className"]);return l.createElement(c,(0,s.Cl)({className:(0,d.A)("card-header",n)},o,{ref:r}),a)}));n.propTypes={as:c().elementType,children:c().node,className:c().string},n.displayName="CCardHeader"},30578:(e,r,a)=>{a.d(r,{E:()=>o});var s=a(3035),l=a(9950),t=a(11942),c=a.n(t),d=a(69344),n=a(3319),o=(0,l.forwardRef)((function(e,r){var a,t=e.children,c=e.className,n=e.color,o=e.textBgColor,i=e.textColor,m=(0,s.Tt)(e,["children","className","color","textBgColor","textColor"]);return l.createElement("div",(0,s.Cl)({className:(0,d.A)("card",(a={},a["bg-".concat(n)]=n,a["text-".concat(i)]=i,a["text-bg-".concat(o)]=o,a),c)},m,{ref:r}),t)}));o.propTypes={children:c().node,className:c().string,color:n.TX,textBgColor:n.TX,textColor:c().string},o.displayName="CCard"},67364:(e,r,a)=>{a.r(r),a.d(r,{default:()=>d});a(9950);var s=a(30578),l=a(13019),t=a(98114),c=a(44414);const d=()=>(0,c.jsxs)(s.E,{children:[(0,c.jsx)(l.V,{children:(0,c.jsx)("strong",{children:"New Permit to Work"})}),(0,c.jsx)(t.W,{children:(0,c.jsx)("p",{children:"Permit form - To be implemented"})})]})},98114:(e,r,a)=>{a.d(r,{W:()=>n});var s=a(3035),l=a(9950),t=a(11942),c=a.n(t),d=a(69344),n=(0,l.forwardRef)((function(e,r){var a=e.children,t=e.className,c=(0,s.Tt)(e,["children","className"]);return l.createElement("div",(0,s.Cl)({className:(0,d.A)("card-body",t)},c,{ref:r}),a)}));n.propTypes={children:c().node,className:c().string},n.displayName="CCardBody"}}]);