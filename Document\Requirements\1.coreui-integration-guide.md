# CoreUI Integration Guide for Digital HSE .NET 8 Solution

## Executive Summary

Integrate CoreUI React directly into your existing .NET 8 Web project. This approach maintains your clean architecture while adding a professional React UI framework that accelerates HSE interface development.

## Integration Architecture

### Recommended Folder Structure

```
digital-hse/
├── src/
│   ├── DigitalHSE.Web/                    # Your existing Web API project
│   │   ├── ClientApp/                     # ADD: CoreUI React App HERE
│   │   │   ├── public/
│   │   │   │   ├── index.html
│   │   │   │   └── manifest.json
│   │   │   ├── src/
│   │   │   │   ├── assets/                # CoreUI assets
│   │   │   │   │   └── brand/
│   │   │   │   ├── components/            # Shared components
│   │   │   │   │   ├── hse/              # HSE-specific components
│   │   │   │   │   │   ├── IncidentForm.tsx
│   │   │   │   │   │   ├── RiskMatrix.tsx
│   │   │   │   │   │   └── PermitCard.tsx
│   │   │   │   │   └── shared/
│   │   │   │   ├── containers/            # CoreUI containers
│   │   │   │   │   └── TheLayout.tsx
│   │   │   │   ├── scss/                  # CoreUI styles
│   │   │   │   │   ├── _custom.scss       # HSE customizations
│   │   │   │   │   └── style.scss
│   │   │   │   ├── views/                 # HSE pages
│   │   │   │   │   ├── incidents/
│   │   │   │   │   ├── risk-assessment/
│   │   │   │   │   ├── compliance/
│   │   │   │   │   └── dashboard/
│   │   │   │   ├── App.tsx
│   │   │   │   └── index.tsx
│   │   │   ├── package.json
│   │   │   └── tsconfig.json
│   │   ├── Controllers/                   # Your existing API controllers
│   │   ├── Program.cs                     # Modified to serve SPA
│   │   └── DigitalHSE.Web.csproj         # Modified to build React
│   ├── DigitalHSE.Application/           # Unchanged
│   ├── DigitalHSE.Domain/                # Unchanged
│   └── DigitalHSE.Infrastructure/        # Unchanged
```

## Step-by-Step Integration Guide

### Step 1: Install CoreUI React Template

```bash
# Navigate to your Web project
cd src/DigitalHSE.Web

# Create CoreUI React app
npx @coreui/coreui-react-app@latest ClientApp --template typescript

# Or if you want to add to existing React app:
cd ClientApp
npm install @coreui/react @coreui/react-chartjs @coreui/chartjs @coreui/coreui @coreui/icons-react
```

### Step 2: Modify DigitalHSE.Web.csproj

```xml
<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <SpaRoot>ClientApp\</SpaRoot>
    <DefaultItemExcludes>$(DefaultItemExcludes);$(SpaRoot)node_modules\**</DefaultItemExcludes>
  </PropertyGroup>

  <!-- Add SPA files to project -->
  <ItemGroup>
    <Content Remove="$(SpaRoot)**" />
    <None Remove="$(SpaRoot)**" />
    <None Include="$(SpaRoot)**" Exclude="$(SpaRoot)node_modules\**" />
  </ItemGroup>

  <!-- Build React app during publish -->
  <Target Name="DebugEnsureNodeEnv" BeforeTargets="Build" Condition=" '$(Configuration)' == 'Debug' And !Exists('$(SpaRoot)node_modules') ">
    <Exec Command="node --version" ContinueOnError="true">
      <Output TaskParameter="ExitCode" PropertyName="ErrorCode" />
    </Exec>
    <Error Condition="'$(ErrorCode)' != '0'" Text="Node.js is required to build and run this project." />
    <Message Importance="high" Text="Restoring dependencies using 'npm'. This may take several minutes..." />
    <Exec WorkingDirectory="$(SpaRoot)" Command="npm install" />
  </Target>

  <Target Name="PublishRunWebpack" AfterTargets="ComputeFilesToPublish">
    <Exec WorkingDirectory="$(SpaRoot)" Command="npm install" />
    <Exec WorkingDirectory="$(SpaRoot)" Command="npm run build" />
    <ItemGroup>
      <DistFiles Include="$(SpaRoot)build\**" />
      <ResolvedFileToPublish Include="@(DistFiles->'%(FullPath)')" Exclude="@(ResolvedFileToPublish)">
        <RelativePath>wwwroot\%(RecursiveDir)%(Filename)%(Extension)</RelativePath>
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      </ResolvedFileToPublish>
    </ItemGroup>
  </Target>
</Project>
```

### Step 3: Configure Program.cs for SPA

```csharp
// Program.cs
var builder = WebApplication.CreateBuilder(args);

// Add services
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Add SPA static files
builder.Services.AddSpaStaticFiles(configuration =>
{
    configuration.RootPath = "ClientApp/build";
});

var app = builder.Build();

// Configure pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseSpaStaticFiles();

app.UseRouting();
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

// Serve React SPA
app.UseSpa(spa =>
{
    spa.Options.SourcePath = "ClientApp";

    if (app.Environment.IsDevelopment())
    {
        // Use React development server
        spa.UseReactDevelopmentServer(npmScript: "start");
    }
});

app.Run();
```

### Step 4: Configure CoreUI for HSE

```typescript
// ClientApp/src/scss/_custom.scss
// HSE-specific color scheme
:root {
  --cui-primary: #1b5e20;        // HSE Green
  --cui-success: #2e7d32;
  --cui-info: #0277bd;
  --cui-warning: #f57c00;
  --cui-danger: #c62828;
  --cui-secondary: #546e7a;
}

// HSE-specific components
.hse-risk-matrix {
  .risk-extreme { background-color: #c62828; }
  .risk-high { background-color: #f57c00; }
  .risk-medium { background-color: #fbc02d; }
  .risk-low { background-color: #689f38; }
}
```

### Step 5: Create HSE-Specific Navigation

```typescript
// ClientApp/src/_nav.tsx
import { CNavItem, CNavGroup } from '@coreui/react'
import CIcon from '@coreui/icons-react'
import {
  cilSpeedometer,
  cilWarning,
  cilTask,
  cilClipboard,
  cilEducation,
  cilFile,
  cilChartPie,
} from '@coreui/icons'

const _nav = [
  {
    component: CNavItem,
    name: 'Dashboard',
    to: '/dashboard',
    icon: <CIcon icon={cilSpeedometer} customClassName="nav-icon" />,
  },
  {
    component: CNavGroup,
    name: 'Incidents',
    to: '/incidents',
    icon: <CIcon icon={cilWarning} customClassName="nav-icon" />,
    items: [
      { component: CNavItem, name: 'Report Incident', to: '/incidents/new' },
      { component: CNavItem, name: 'Incident List', to: '/incidents/list' },
      { component: CNavItem, name: 'Investigations', to: '/incidents/investigations' },
    ],
  },
  {
    component: CNavGroup,
    name: 'Risk Management',
    to: '/risks',
    icon: <CIcon icon={cilTask} customClassName="nav-icon" />,
    items: [
      { component: CNavItem, name: 'Risk Matrix', to: '/risks/matrix' },
      { component: CNavItem, name: 'Risk Assessments', to: '/risks/assessments' },
      { component: CNavItem, name: 'Control Measures', to: '/risks/controls' },
    ],
  },
  {
    component: CNavItem,
    name: 'Permit to Work',
    to: '/permits',
    icon: <CIcon icon={cilClipboard} customClassName="nav-icon" />,
  },
  {
    component: CNavItem,
    name: 'Training',
    to: '/training',
    icon: <CIcon icon={cilEducation} customClassName="nav-icon" />,
  },
  {
    component: CNavItem,
    name: 'Documents',
    to: '/documents',
    icon: <CIcon icon={cilFile} customClassName="nav-icon" />,
  },
  {
    component: CNavItem,
    name: 'Analytics',
    to: '/analytics',
    icon: <CIcon icon={cilChartPie} customClassName="nav-icon" />,
  },
]

export default _nav
```

### Step 6: Create HSE Dashboard

```typescript
// ClientApp/src/views/dashboard/HSEDashboard.tsx
import React, { useEffect, useState } from 'react'
import {
  CCard,
  CCardBody,
  CCardHeader,
  CCol,
  CRow,
  CWidgetStatsA,
  CChartBar,
  CChartLine,
} from '@coreui/react'
import { CChartDoughnut } from '@coreui/react-chartjs'
import { useApi } from '../../hooks/useApi'

const HSEDashboard: React.FC = () => {
  const { data: stats, loading } = useApi('/api/dashboard/stats')

  return (
    <>
      <CRow>
        <CCol sm={6} lg={3}>
          <CWidgetStatsA
            className="mb-4"
            color="danger"
            value={stats?.openIncidents || '0'}
            title="Open Incidents"
            chart={
              <CChartLine
                className="mt-3 mx-3"
                style={{ height: '70px' }}
                data={{
                  labels: stats?.incidentTrend?.labels || [],
                  datasets: [{
                    label: 'Incidents',
                    data: stats?.incidentTrend?.data || [],
                    borderColor: 'rgba(255,255,255,.55)',
                  }],
                }}
              />
            }
          />
        </CCol>
        <CCol sm={6} lg={3}>
          <CWidgetStatsA
            className="mb-4"
            color="warning"
            value={stats?.overdueActions || '0'}
            title="Overdue Actions"
          />
        </CCol>
        <CCol sm={6} lg={3}>
          <CWidgetStatsA
            className="mb-4"
            color="success"
            value={`${stats?.trainingCompliance || 0}%`}
            title="Training Compliance"
          />
        </CCol>
        <CCol sm={6} lg={3}>
          <CWidgetStatsA
            className="mb-4"
            color="info"
            value={stats?.activePermits || '0'}
            title="Active Permits"
          />
        </CCol>
      </CRow>

      <CRow>
        <CCol xs={12} md={6}>
          <CCard className="mb-4">
            <CCardHeader>Risk Distribution</CCardHeader>
            <CCardBody>
              <CChartDoughnut
                data={{
                  labels: ['Low', 'Medium', 'High', 'Extreme'],
                  datasets: [{
                    data: stats?.riskDistribution || [0, 0, 0, 0],
                    backgroundColor: ['#689f38', '#fbc02d', '#f57c00', '#c62828'],
                  }],
                }}
              />
            </CCardBody>
          </CCard>
        </CCol>
        <CCol xs={12} md={6}>
          <CCard className="mb-4">
            <CCardHeader>Incidents by Department</CCardHeader>
            <CCardBody>
              <CChartBar
                data={{
                  labels: stats?.departments || [],
                  datasets: [{
                    label: 'Incidents',
                    data: stats?.departmentIncidents || [],
                    backgroundColor: '#1b5e20',
                  }],
                }}
              />
            </CCardBody>
          </CCard>
        </CCol>
      </CRow>
    </>
  )
}

export default HSEDashboard
```

### Step 7: API Integration Layer

```typescript
// ClientApp/src/services/api.ts
import axios from 'axios'

const API_BASE_URL = process.env.REACT_APP_API_URL || '/api'

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// Handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

export default api

// HSE-specific API services
export const hseApi = {
  // Incidents
  getIncidents: () => api.get('/incidents'),
  createIncident: (data: any) => api.post('/incidents', data),
  getIncident: (id: string) => api.get(`/incidents/${id}`),
  
  // Risk Assessment
  getRiskMatrix: () => api.get('/risks/matrix'),
  createRiskAssessment: (data: any) => api.post('/risks/assessments', data),
  
  // Permits
  getActivePermits: () => api.get('/permits/active'),
  createPermit: (data: any) => api.post('/permits', data),
}
```

### Step 8: TypeScript Type Generation

```json
// ClientApp/package.json - Add scripts
{
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "generate-types": "openapi-typescript http://localhost:5000/swagger/v1/swagger.json --output ./src/types/api.d.ts"
  },
  "devDependencies": {
    "openapi-typescript": "^6.7.0"
  }
}
```

### Step 9: Development Workflow Configuration

```json
// .vscode/tasks.json
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "Run Full Stack",
      "dependsOn": ["Run Backend", "Run Frontend"],
      "group": {
        "kind": "build",
        "isDefault": true
      }
    },
    {
      "label": "Run Backend",
      "type": "shell",
      "command": "dotnet",
      "args": ["watch", "run"],
      "options": {
        "cwd": "${workspaceFolder}/src/DigitalHSE.Web"
      }
    },
    {
      "label": "Run Frontend",
      "type": "shell",
      "command": "npm",
      "args": ["start"],
      "options": {
        "cwd": "${workspaceFolder}/src/DigitalHSE.Web/ClientApp"
      }
    }
  ]
}
```

## Benefits of This Integration

### 1. **Unified Deployment**
```dockerfile
# Single Dockerfile for both frontend and backend
FROM node:18 AS frontend-build
WORKDIR /app/ClientApp
COPY src/DigitalHSE.Web/ClientApp/package*.json ./
RUN npm ci
COPY src/DigitalHSE.Web/ClientApp/ ./
RUN npm run build

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS backend-build
WORKDIR /src
COPY . .
RUN dotnet publish -c Release -o /app/publish

FROM mcr.microsoft.com/dotnet/aspnet:8.0
WORKDIR /app
COPY --from=backend-build /app/publish .
COPY --from=frontend-build /app/ClientApp/build ./wwwroot
EXPOSE 80
ENTRYPOINT ["dotnet", "DigitalHSE.Web.dll"]
```

### 2. **Shared Authentication**
```csharp
// Use same JWT tokens for API and SPA
[Authorize]
[ApiController]
[Route("api/[controller]")]
public class IncidentsController : ControllerBase
{
    // API endpoints protected by same auth as SPA
}
```

### 3. **Type Safety**
```bash
# Generate TypeScript types from C# models
dotnet swagger tofile --output swagger.json DigitalHSE.Web.dll v1
npm run generate-types
```

### 4. **Hot Reload Development**
- Backend: `dotnet watch run` auto-reloads C# changes
- Frontend: React dev server auto-reloads TypeScript changes
- Both work simultaneously

## CoreUI Components for HSE

### Risk Matrix Component
```typescript
// ClientApp/src/components/hse/RiskMatrix.tsx
import React from 'react'
import { CCard, CCardBody, CTable } from '@coreui/react'

const RiskMatrix: React.FC = () => {
  const getRiskClass = (likelihood: number, consequence: number) => {
    const score = likelihood * consequence
    if (score >= 20) return 'risk-extreme'
    if (score >= 15) return 'risk-high'
    if (score >= 10) return 'risk-medium'
    return 'risk-low'
  }

  return (
    <CCard>
      <CCardBody>
        <CTable bordered>
          {/* Risk matrix implementation */}
        </CTable>
      </CCardBody>
    </CCard>
  )
}
```

### Incident Form Component
```typescript
// ClientApp/src/components/hse/IncidentForm.tsx
import React from 'react'
import {
  CForm,
  CFormInput,
  CFormSelect,
  CFormTextarea,
  CButton,
  CCard,
  CCardBody,
} from '@coreui/react'
import { useForm } from 'react-hook-form'

const IncidentForm: React.FC = () => {
  const { register, handleSubmit } = useForm()

  const onSubmit = async (data: any) => {
    await hseApi.createIncident(data)
  }

  return (
    <CCard>
      <CCardBody>
        <CForm onSubmit={handleSubmit(onSubmit)}>
          <CFormInput
            {...register('title')}
            label="Incident Title"
            required
          />
          <CFormSelect
            {...register('severity')}
            label="Severity"
            options={[
              { value: 1, label: 'Minor' },
              { value: 2, label: 'Moderate' },
              { value: 3, label: 'Major' },
              { value: 4, label: 'Severe' },
              { value: 5, label: 'Critical' },
            ]}
          />
          <CFormTextarea
            {...register('description')}
            label="Description"
            rows={4}
          />
          <CButton type="submit" color="primary">
            Submit Report
          </CButton>
        </CForm>
      </CCardBody>
    </CCard>
  )
}
```

## Migration Timeline

### Week 1-2: Setup
- Install CoreUI in Web project
- Configure SPA hosting
- Setup development workflow

### Week 3-4: Core UI
- Implement navigation
- Create dashboard
- Setup API integration

### Week 5-8: HSE Modules
- Incident management UI
- Risk assessment UI
- Permit system UI
- Compliance tracking UI

### Week 9-10: Polish
- Multi-language support
- Mobile responsiveness
- Performance optimization

### Week 11-12: Testing
- Integration testing
- User acceptance testing
- Deployment preparation

## Cost Impact

**No additional licensing costs** - CoreUI React is MIT licensed for the open-source version.

**Development time savings**: 30-40% faster than building custom UI components.

This integration approach gives you a professional HSE interface while maintaining your clean architecture and allowing your single developer to work efficiently across the full stack.