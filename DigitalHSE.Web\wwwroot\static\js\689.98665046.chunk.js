"use strict";(self.webpackChunkdigitalhse_web=self.webpackChunkdigitalhse_web||[]).push([[689],{13019:(e,r,a)=>{a.d(r,{V:()=>d});var s=a(3035),l=a(9950),c=a(11942),t=a.n(c),n=a(69344),d=(0,l.forwardRef)((function(e,r){var a=e.children,c=e.as,t=void 0===c?"div":c,d=e.className,o=(0,s.Tt)(e,["children","as","className"]);return l.createElement(t,(0,s.Cl)({className:(0,n.A)("card-header",d)},o,{ref:r}),a)}));d.propTypes={as:t().elementType,children:t().node,className:t().string},d.displayName="CCardHeader"},30578:(e,r,a)=>{a.d(r,{E:()=>o});var s=a(3035),l=a(9950),c=a(11942),t=a.n(c),n=a(69344),d=a(3319),o=(0,l.forwardRef)((function(e,r){var a,c=e.children,t=e.className,d=e.color,o=e.textBgColor,i=e.textColor,m=(0,s.Tt)(e,["children","className","color","textBgColor","textColor"]);return l.createElement("div",(0,s.Cl)({className:(0,n.A)("card",(a={},a["bg-".concat(d)]=d,a["text-".concat(i)]=i,a["text-bg-".concat(o)]=o,a),t)},m,{ref:r}),c)}));o.propTypes={children:t().node,className:t().string,color:d.TX,textBgColor:d.TX,textColor:t().string},o.displayName="CCard"},92689:(e,r,a)=>{a.r(r),a.d(r,{default:()=>n});a(9950);var s=a(30578),l=a(13019),c=a(98114),t=a(44414);const n=()=>(0,t.jsxs)(s.E,{children:[(0,t.jsx)(l.V,{children:(0,t.jsx)("strong",{children:"Document Library"})}),(0,t.jsx)(c.W,{children:(0,t.jsx)("p",{children:"HSE documents - To be implemented"})})]})},98114:(e,r,a)=>{a.d(r,{W:()=>d});var s=a(3035),l=a(9950),c=a(11942),t=a.n(c),n=a(69344),d=(0,l.forwardRef)((function(e,r){var a=e.children,c=e.className,t=(0,s.Tt)(e,["children","className"]);return l.createElement("div",(0,s.Cl)({className:(0,n.A)("card-body",c)},t,{ref:r}),a)}));d.propTypes={children:t().node,className:t().string},d.displayName="CCardBody"}}]);