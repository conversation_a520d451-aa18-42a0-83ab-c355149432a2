"use strict";(self.webpackChunkdigitalhse_web=self.webpackChunkdigitalhse_web||[]).push([[668],{5356:(e,a,r)=>{r.d(a,{s:()=>i});var n=r(3035),s=r(9950),t=r(11942),l=r.n(t),o=r(69344),i=(0,s.forwardRef)((function(e,a){var r=e.children,t=e.as,l=void 0===t?"span":t,i=e.className,c=(0,n.Tt)(e,["children","as","className"]);return s.createElement(l,(0,n.Cl)({className:(0,o.A)("input-group-text",i)},c,{ref:a}),r)}));i.propTypes={as:l().elementType,children:l().node,className:l().string},i.displayName="CInputGroupText"},9134:(e,a,r)=>{r.d(a,{k:()=>p});var n=r(3035),s=r(9950),t=r(11942),l=r.n(t),o=r(69344),i=r(23793),c=r(49115),d=r(3319),m=r(92729),p=(0,s.forwardRef)((function(e,a){var r=e.children,t=e.className,l=e.color,d=void 0===l?"primary":l,p=e.dismissible,f=e.variant,u=e.visible,h=void 0===u||u,b=e.onClose,g=(0,n.Tt)(e,["children","className","color","dismissible","variant","visible","onClose"]),v=(0,s.useRef)(null),y=(0,c.E2)(a,v),N=(0,s.useState)(h),x=N[0],T=N[1];return(0,s.useEffect)((function(){T(h)}),[h]),s.createElement(m.Ay,{in:x,mountOnEnter:!0,nodeRef:v,onExit:b,timeout:150,unmountOnExit:!0},(function(e){return s.createElement("div",(0,n.Cl)({className:(0,o.A)("alert","solid"===f?"bg-".concat(d," text-white"):"alert-".concat(d),{"alert-dismissible fade":p,show:"entered"===e},t),role:"alert"},g,{ref:y}),r,p&&s.createElement(i.E,{onClick:function(){return T(!1)}}))}))}));p.propTypes={children:l().node,className:l().string,color:d.TX.isRequired,dismissible:l().bool,onClose:l().func,variant:l().string,visible:l().bool},p.displayName="CAlert"},23793:(e,a,r)=>{r.d(a,{E:()=>i});var n=r(3035),s=r(9950),t=r(11942),l=r.n(t),o=r(69344),i=(0,s.forwardRef)((function(e,a){var r=e.className,t=e.dark,l=e.disabled,i=e.white,c=(0,n.Tt)(e,["className","dark","disabled","white"]);return s.createElement("button",(0,n.Cl)({type:"button",className:(0,o.A)("btn","btn-close",{"btn-close-white":i},l,r),"aria-label":"Close",disabled:l},t&&{"data-coreui-theme":"dark"},c,{ref:a}))}));i.propTypes={className:l().string,dark:l().bool,disabled:l().bool,white:l().bool},i.displayName="CCloseButton"},30578:(e,a,r)=>{r.d(a,{E:()=>c});var n=r(3035),s=r(9950),t=r(11942),l=r.n(t),o=r(69344),i=r(3319),c=(0,s.forwardRef)((function(e,a){var r,t=e.children,l=e.className,i=e.color,c=e.textBgColor,d=e.textColor,m=(0,n.Tt)(e,["children","className","color","textBgColor","textColor"]);return s.createElement("div",(0,n.Cl)({className:(0,o.A)("card",(r={},r["bg-".concat(i)]=i,r["text-".concat(d)]=d,r["text-bg-".concat(c)]=c,r),l)},m,{ref:a}),t)}));c.propTypes={children:l().node,className:l().string,color:i.TX,textBgColor:i.TX,textColor:l().string},c.displayName="CCard"},30668:(e,a,r)=>{r.r(a),r.d(a,{default:()=>O});var n=r(89379),s=r(9950),t=r(28429),l=r(64771),o=r(52684),i=r(71398),c=r(3035),d=r(11942),m=r.n(d),p=r(69344),f=(0,s.forwardRef)((function(e,a){var r=e.children,n=e.className,t=(0,c.Tt)(e,["children","className"]);return s.createElement("div",(0,c.Cl)({className:(0,p.A)("card-group",n)},t,{ref:a}),r)}));f.propTypes={children:m().node,className:m().string},f.displayName="CCardGroup";var u=r(30578),h=r(98114),b=r(60538),g=r(9134),v=r(64831),y=r(5356),N=r(68852),x=r(77641),T=r(61114),C=r(39696),w=r(81949),j=r(62293),E=["512 512","<path fill='var(--ci-primary-color, currentColor)' d='M384,200V144a128,128,0,0,0-256,0v56H88V328c0,92.635,75.364,168,168,168s168-75.365,168-168V200ZM160,144a96,96,0,0,1,192,0v56H160ZM392,328c0,74.99-61.01,136-136,136s-136-61.01-136-136V232H392Z' class='ci-primary'/>"],k=r(44414);const O=()=>{const[e,a]=(0,s.useState)({username:"",password:""}),[r,c]=(0,s.useState)(!1),[d,m]=(0,s.useState)(""),[p,O]=(0,s.useState)({}),A=(0,t.Zp)(),F=(e,r)=>{a((a=>(0,n.A)((0,n.A)({},a),{},{[e]:r}))),p[e]&&O((a=>(0,n.A)((0,n.A)({},a),{},{[e]:""}))),d&&m("")};return(0,k.jsx)("div",{className:"bg-light min-vh-100 d-flex flex-row align-items-center",children:(0,k.jsx)(l.T,{children:(0,k.jsx)(o.s,{className:"justify-content-center",children:(0,k.jsx)(i.U,{md:8,children:(0,k.jsxs)(f,{children:[(0,k.jsx)(u.E,{className:"p-4",children:(0,k.jsx)(h.W,{children:(0,k.jsxs)(b.q,{onSubmit:async a=>{if(a.preventDefault(),(()=>{const a={};return e.username.trim()||(a.username="Username is required"),e.password?e.password.length<6&&(a.password="Password must be at least 6 characters"):a.password="Password is required",O(a),0===Object.keys(a).length})()){c(!0),m("");try{const a=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!a.ok){const e=await a.json();throw new Error(e.message||"Login failed")}const r=await a.json();localStorage.setItem("accessToken",r.accessToken),localStorage.setItem("refreshToken",r.refreshToken),localStorage.setItem("user",JSON.stringify(r.user)),A("/dashboard")}catch(r){m(r instanceof Error?r.message:"An unexpected error occurred")}finally{c(!1)}}},noValidate:!0,children:[(0,k.jsx)("h1",{children:"Login"}),(0,k.jsx)("p",{className:"text-medium-emphasis",children:"Sign In to your account"}),d&&(0,k.jsx)(g.k,{color:"danger",className:"mb-3",children:d}),(0,k.jsxs)(v.B,{className:"mb-3",children:[(0,k.jsx)(y.s,{children:(0,k.jsx)(w.Ay,{icon:j.o})}),(0,k.jsx)(N.O,{placeholder:"Username",autoComplete:"username",value:e.username,onChange:e=>F("username",e.target.value),invalid:!!p.username,disabled:r}),(0,k.jsx)(x.T,{invalid:!0,children:p.username})]}),(0,k.jsxs)(v.B,{className:"mb-4",children:[(0,k.jsx)(y.s,{children:(0,k.jsx)(w.Ay,{icon:E})}),(0,k.jsx)(N.O,{type:"password",placeholder:"Password",autoComplete:"current-password",value:e.password,onChange:e=>F("password",e.target.value),invalid:!!p.password,disabled:r}),(0,k.jsx)(x.T,{invalid:!0,children:p.password})]}),(0,k.jsxs)(o.s,{children:[(0,k.jsx)(i.U,{xs:6,children:(0,k.jsx)(T.Q,{color:"primary",className:"px-4",type:"submit",disabled:r,children:r?(0,k.jsxs)(k.Fragment,{children:[(0,k.jsx)(C.J,{size:"sm",className:"me-2"}),"Signing in..."]}):"Login"})}),(0,k.jsx)(i.U,{xs:6,className:"text-end",children:(0,k.jsx)(T.Q,{color:"link",className:"px-0",disabled:r,onClick:()=>{alert("Forgot password functionality will be implemented soon")},children:"Forgot password?"})})]})]})})}),(0,k.jsx)(u.E,{className:"text-white bg-primary py-5",style:{width:"44%"},children:(0,k.jsx)(h.W,{className:"text-center",children:(0,k.jsxs)("div",{children:[(0,k.jsx)("h2",{children:"Digital HSE"}),(0,k.jsx)("p",{className:"mt-3",children:"Health, Safety & Environment Management System"}),(0,k.jsx)("p",{children:(0,k.jsx)("strong",{children:"The British School Jakarta"})}),(0,k.jsx)("p",{className:"mt-4",children:"Ensuring a safe and healthy environment for our school community"}),(0,k.jsx)("div",{className:"mt-4",children:(0,k.jsx)("small",{className:"text-white-50",children:"Secure access to HSE management tools and incident reporting"})}),(0,k.jsx)("div",{className:"mt-4",children:(0,k.jsxs)("small",{className:"text-white-75",children:[(0,k.jsx)("strong",{children:"Demo Accounts:"}),(0,k.jsx)("br",{}),"Admin: admin / admin123",(0,k.jsx)("br",{}),"HSE Manager: hsemanager / hse123",(0,k.jsx)("br",{}),"HSE Officer: hseofficer / hse123",(0,k.jsx)("br",{}),"Teacher: teacher / teacher123",(0,k.jsx)("br",{}),"Student: student / student123"]})})]})})})]})})})})})}},52684:(e,a,r)=>{r.d(a,{s:()=>c});var n=r(3035),s=r(9950),t=r(11942),l=r.n(t),o=r(69344),i=["xxl","xl","lg","md","sm","xs"],c=(0,s.forwardRef)((function(e,a){var r=e.children,t=e.className,l=(0,n.Tt)(e,["children","className"]),c=[];return i.forEach((function(e){var a=l[e];delete l[e];var r="xs"===e?"":"-".concat(e);"object"===typeof a&&(a.cols&&c.push("row-cols".concat(r,"-").concat(a.cols)),"number"===typeof a.gutter&&c.push("g".concat(r,"-").concat(a.gutter)),"number"===typeof a.gutterX&&c.push("gx".concat(r,"-").concat(a.gutterX)),"number"===typeof a.gutterY&&c.push("gy".concat(r,"-").concat(a.gutterY)))})),s.createElement("div",(0,n.Cl)({className:(0,o.A)("row",c,t)},l,{ref:a}),r)})),d=l().shape({cols:l().oneOfType([l().oneOf(["auto"]),l().number,l().string]),gutter:l().oneOfType([l().string,l().number]),gutterX:l().oneOfType([l().string,l().number]),gutterY:l().oneOfType([l().string,l().number])});c.propTypes={children:l().node,className:l().string,xs:d,sm:d,md:d,lg:d,xl:d,xxl:d},c.displayName="CRow"},60538:(e,a,r)=>{r.d(a,{q:()=>i});var n=r(3035),s=r(9950),t=r(11942),l=r.n(t),o=r(69344),i=(0,s.forwardRef)((function(e,a){var r=e.children,t=e.className,l=e.validated,i=(0,n.Tt)(e,["children","className","validated"]);return s.createElement("form",(0,n.Cl)({className:(0,o.A)({"was-validated":l},t)||void 0},i,{ref:a}),r)}));i.propTypes={children:l().node,className:l().string,validated:l().bool},i.displayName="CForm"},64831:(e,a,r)=>{r.d(a,{B:()=>i});var n=r(3035),s=r(9950),t=r(11942),l=r.n(t),o=r(69344),i=(0,s.forwardRef)((function(e,a){var r,t=e.children,l=e.className,i=e.size,c=(0,n.Tt)(e,["children","className","size"]);return s.createElement("div",(0,n.Cl)({className:(0,o.A)("input-group",(r={},r["input-group-".concat(i)]=i,r),l)},c,{ref:a}),t)}));i.propTypes={children:l().node,className:l().string,size:l().oneOf(["sm","lg"])},i.displayName="CInputGroup"},68852:(e,a,r)=>{r.d(a,{O:()=>c});var n=r(3035),s=r(9950),t=r(11942),l=r.n(t),o=r(69344),i=r(80989),c=(0,s.forwardRef)((function(e,a){var r,t=e.children,l=e.className,c=e.delay,d=void 0!==c&&c,m=e.feedback,p=e.feedbackInvalid,f=e.feedbackValid,u=e.floatingClassName,h=e.floatingLabel,b=e.id,g=e.invalid,v=e.label,y=e.onChange,N=e.plainText,x=e.size,T=e.text,C=e.tooltipFeedback,w=e.type,j=void 0===w?"text":w,E=e.valid,k=(0,n.Tt)(e,["children","className","delay","feedback","feedbackInvalid","feedbackValid","floatingClassName","floatingLabel","id","invalid","label","onChange","plainText","size","text","tooltipFeedback","type","valid"]),O=(0,s.useState)(),A=O[0],F=O[1];return(0,s.useEffect)((function(){var e=setTimeout((function(){return A&&y&&y(A)}),"number"===typeof d?d:500);return function(){return clearTimeout(e)}}),[A]),s.createElement(i.O,{describedby:k["aria-describedby"],feedback:m,feedbackInvalid:p,feedbackValid:f,floatingClassName:u,floatingLabel:h,id:b,invalid:g,label:v,text:T,tooltipFeedback:C,valid:E},s.createElement("input",(0,n.Cl)({className:(0,o.A)(N?"form-control-plaintext":"form-control",(r={},r["form-control-".concat(x)]=x,r["form-control-color"]="color"===j,r["is-invalid"]=g,r["is-valid"]=E,r),l),id:b,type:j,onChange:function(e){return d?F(e):y&&y(e)}},k,{ref:a}),t))}));c.propTypes=(0,n.Cl)({className:l().string,id:l().string,delay:l().oneOfType([l().bool,l().number]),plainText:l().bool,size:l().oneOf(["sm","lg"]),type:l().oneOfType([l().oneOf(["color","file","text"]),l().string])},i.O.propTypes),c.displayName="CFormInput"},71398:(e,a,r)=>{r.d(a,{U:()=>c});var n=r(3035),s=r(9950),t=r(11942),l=r.n(t),o=r(69344),i=["xxl","xl","lg","md","sm","xs"],c=(0,s.forwardRef)((function(e,a){var r=e.children,t=e.className,l=(0,n.Tt)(e,["children","className"]),c=[];return i.forEach((function(e){var a=l[e];delete l[e];var r="xs"===e?"":"-".concat(e);"number"!==typeof a&&"string"!==typeof a||c.push("col".concat(r,"-").concat(a)),"boolean"===typeof a&&c.push("col".concat(r)),a&&"object"===typeof a&&("number"!==typeof a.span&&"string"!==typeof a.span||c.push("col".concat(r,"-").concat(a.span)),"boolean"===typeof a.span&&c.push("col".concat(r)),"number"!==typeof a.order&&"string"!==typeof a.order||c.push("order".concat(r,"-").concat(a.order)),"number"===typeof a.offset&&c.push("offset".concat(r,"-").concat(a.offset)))})),s.createElement("div",(0,n.Cl)({className:(0,o.A)(c.length>0?c:"col",t)},l,{ref:a}),r)})),d=l().oneOfType([l().bool,l().number,l().string,l().oneOf(["auto"])]),m=l().oneOfType([d,l().shape({span:d,offset:l().oneOfType([l().number,l().string]),order:l().oneOfType([l().oneOf(["first","last"]),l().number,l().string])})]);c.propTypes={children:l().node,className:l().string,xs:m,sm:m,md:m,lg:m,xl:m,xxl:m},c.displayName="CCol"},76818:(e,a,r)=>{r.d(a,{_:()=>i});var n=r(3035),s=r(9950),t=r(11942),l=r.n(t),o=r(77641),i=function(e){var a=e.describedby,r=e.feedback,t=e.feedbackInvalid,l=e.feedbackValid,i=e.invalid,c=e.tooltipFeedback,d=e.valid;return s.createElement(s.Fragment,null,r&&(d||i)&&s.createElement(o.T,(0,n.Cl)({},i&&{id:a},{invalid:i,tooltip:c,valid:d}),r),t&&s.createElement(o.T,{id:a,invalid:!0,tooltip:c},t),l&&s.createElement(o.T,{valid:!0,tooltip:c},l))};i.propTypes={describedby:l().string,feedback:l().oneOfType([l().node,l().string]),feedbackValid:l().oneOfType([l().node,l().string]),feedbackInvalid:l().oneOfType([l().node,l().string]),invalid:l().bool,tooltipFeedback:l().bool,valid:l().bool},i.displayName="CFormControlValidation"},77641:(e,a,r)=>{r.d(a,{T:()=>i});var n=r(3035),s=r(9950),t=r(11942),l=r.n(t),o=r(69344),i=(0,s.forwardRef)((function(e,a){var r,t=e.children,l=e.as,i=void 0===l?"div":l,c=e.className,d=e.invalid,m=e.tooltip,p=e.valid,f=(0,n.Tt)(e,["children","as","className","invalid","tooltip","valid"]);return s.createElement(i,(0,n.Cl)({className:(0,o.A)((r={},r["invalid-".concat(m?"tooltip":"feedback")]=d,r["valid-".concat(m?"tooltip":"feedback")]=p,r),c)},f,{ref:a}),t)}));i.propTypes={as:l().elementType,children:l().node,className:l().string,invalid:l().bool,tooltip:l().bool,valid:l().bool},i.displayName="CFormFeedback"},78402:(e,a,r)=>{r.d(a,{A:()=>i});var n=r(3035),s=r(9950),t=r(11942),l=r.n(t),o=r(69344),i=(0,s.forwardRef)((function(e,a){var r=e.children,t=e.className,l=e.customClassName,i=(0,n.Tt)(e,["children","className","customClassName"]);return s.createElement("label",(0,n.Cl)({className:null!==l&&void 0!==l?l:(0,o.A)("form-label",t)},i,{ref:a}),r)}));i.propTypes={children:l().node,className:l().string,customClassName:l().string},i.displayName="CFormLabel"},80989:(e,a,r)=>{r.d(a,{O:()=>p});var n=r(3035),s=r(9950),t=r(11942),l=r.n(t),o=r(76818),i=r(69344),c=(0,s.forwardRef)((function(e,a){var r=e.children,t=e.className,l=(0,n.Tt)(e,["children","className"]);return s.createElement("div",(0,n.Cl)({className:(0,i.A)("form-floating",t)},l,{ref:a}),r)}));c.propTypes={children:l().node,className:l().string},c.displayName="CFormFloating";var d=r(78402),m=(0,s.forwardRef)((function(e,a){var r=e.children,t=e.as,l=void 0===t?"div":t,o=e.className,c=(0,n.Tt)(e,["children","as","className"]);return s.createElement(l,(0,n.Cl)({className:(0,i.A)("form-text",o)},c,{ref:a}),r)}));m.propTypes={as:l().elementType,children:l().node,className:l().string},m.displayName="CFormText";var p=function(e){var a=e.children,r=e.describedby,n=e.feedback,t=e.feedbackInvalid,l=e.feedbackValid,i=e.floatingClassName,p=e.floatingLabel,f=e.id,u=e.invalid,h=e.label,b=e.text,g=e.tooltipFeedback,v=e.valid,y=function(){return s.createElement(o._,{describedby:r,feedback:n,feedbackInvalid:t,feedbackValid:l,floatingLabel:p,invalid:u,tooltipFeedback:g,valid:v})};return p?s.createElement(c,{className:i},a,s.createElement(d.A,{htmlFor:f},h||p),b&&s.createElement(m,{id:r},b),s.createElement(y,null)):s.createElement(s.Fragment,null,h&&s.createElement(d.A,{htmlFor:f},h),a,b&&s.createElement(m,{id:r},b),s.createElement(y,null))};p.propTypes=(0,n.Cl)({children:l().node,floatingClassName:l().string,floatingLabel:l().oneOfType([l().node,l().string]),label:l().oneOfType([l().node,l().string]),text:l().oneOfType([l().node,l().string])},o._.propTypes),p.displayName="CFormControlWrapper"},98114:(e,a,r)=>{r.d(a,{W:()=>i});var n=r(3035),s=r(9950),t=r(11942),l=r.n(t),o=r(69344),i=(0,s.forwardRef)((function(e,a){var r=e.children,t=e.className,l=(0,n.Tt)(e,["children","className"]);return s.createElement("div",(0,n.Cl)({className:(0,o.A)("card-body",t)},l,{ref:a}),r)}));i.propTypes={children:l().node,className:l().string},i.displayName="CCardBody"}}]);