{"incidents": {"title": "<PERSON><PERSON><PERSON><PERSON>", "create": "Laporkan Insiden Baru", "list": "<PERSON><PERSON><PERSON>n", "details": "<PERSON>ail <PERSON>n", "number": "Nomor Insiden", "type": "<PERSON><PERSON>", "severity": {"minor": "<PERSON><PERSON>", "low": "Rendah", "moderate": "Sedang", "major": "<PERSON><PERSON>", "critical": "<PERSON><PERSON><PERSON>"}, "status": {"reported": "Dilaporkan", "investigating": "<PERSON><PERSON>", "actionRequired": "<PERSON><PERSON>", "closed": "Ditutup"}, "location": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>", "reportedBy": "<PERSON><PERSON><PERSON><PERSON>", "reportedDate": "<PERSON><PERSON>", "incidentDate": "<PERSON><PERSON>", "department": "Departemen", "injuredPersons": "Orang yang <PERSON>", "witnessNames": "<PERSON><PERSON>", "immediateActions": "<PERSON><PERSON><PERSON> yang <PERSON>", "rootCause": "<PERSON><PERSON><PERSON>", "correctiveActions": "<PERSON><PERSON><PERSON>", "preventiveActions": "<PERSON><PERSON><PERSON>", "regulatory": {"bpjsReporting": "Pelaporan BPJS Diperlukan", "ministryReporting": "<PERSON>ela<PERSON><PERSON>", "deadline": "Batas <PERSON>", "bpjsReference": "Nomor Referensi BPJS", "ministryReference": "<PERSON><PERSON>fer<PERSON>"}, "types": {"nearmiss": "<PERSON><PERSON><PERSON>", "firstaid": "<PERSON><PERSON><PERSON><PERSON>", "medical": "Perawatan Medis", "losttime": "<PERSON>dera W<PERSON>", "property": "<PERSON><PERSON><PERSON><PERSON>i", "environmental": "Insiden <PERSON>", "studentInjury": "Cedera Siswa", "staffInjury": "Cedera Staf", "teacherInjury": "<PERSON><PERSON><PERSON>", "playgroundAccident": "Kecelakaan Playground", "sportsInjury": "Cedera Olahraga", "laboratoryAccident": "Kecelakaan Laboratorium", "fieldTripIncident": "<PERSON><PERSON>", "behavioralIncident": "<PERSON>n <PERSON>", "bullyingIncident": "Insiden <PERSON>", "securityIncident": "<PERSON><PERSON>", "foodSafetyIncident": "<PERSON><PERSON>", "transportationIncident": "Insiden Transportasi", "fire": "Ke<PERSON><PERSON><PERSON>", "propertyDamage": "<PERSON><PERSON><PERSON><PERSON>i", "nearMiss": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON><PERSON>"}}, "riskAssessments": {"title": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON>t Pen<PERSON>", "list": "<PERSON><PERSON><PERSON>", "details": "Detail Pen<PERSON>", "activity": "Aktivitas/Proses", "hazards": "<PERSON><PERSON><PERSON> yang <PERSON><PERSON>", "riskLevel": "<PERSON><PERSON><PERSON>", "controlMeasures": "<PERSON><PERSON><PERSON>", "residualRisk": "<PERSON><PERSON><PERSON>", "reviewer": "<PERSON><PERSON><PERSON><PERSON>", "approver": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "validUntil": "<PERSON><PERSON><PERSON><PERSON>", "riskMatrix": "<PERSON><PERSON><PERSON>", "likelihood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "consequence": "Ko<PERSON><PERSON><PERSON><PERSON>"}, "permits": {"title": "<PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "list": "<PERSON><PERSON><PERSON>", "details": "<PERSON><PERSON>", "type": "<PERSON><PERSON>", "workDescription": "<PERSON><PERSON><PERSON><PERSON>", "location": "<PERSON><PERSON>", "startDate": "<PERSON><PERSON>", "endDate": "<PERSON><PERSON>", "contractor": "Kontraktor", "supervisor": "Supervisor", "safetyMeasures": "<PERSON><PERSON><PERSON>", "equipmentRequired": "<PERSON><PERSON><PERSON> yang <PERSON>", "approvals": "<PERSON><PERSON><PERSON><PERSON><PERSON> ya<PERSON>", "status": {"pending": "<PERSON><PERSON><PERSON>", "approved": "Disetuju<PERSON>", "active": "Aktif", "completed": "Se<PERSON><PERSON>", "cancelled": "Di<PERSON><PERSON><PERSON>", "expired": "Kadaluarsa"}, "types": {"hotwork": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confinedspace": "<PERSON><PERSON> Terbatas", "electrical": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "excavation": "Penggalian", "height": "<PERSON><PERSON><PERSON>tinggian", "general": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "compliance": {"title": "<PERSON><PERSON><PERSON><PERSON>", "items": "<PERSON><PERSON>", "requirements": "Persyara<PERSON>", "status": "Status Kepatuhan", "dueDate": "Tanggal Jatuh Tempo", "lastAudit": "<PERSON><PERSON>", "nextAudit": "Audit Berikutnya", "responsible": "Penanggung Jawab", "evidence": "<PERSON>uk<PERSON>/Dokumentasi", "nonCompliances": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "correctiveActions": "<PERSON><PERSON><PERSON>"}, "training": {"title": "Pelatihan K3", "records": "Catatan Pelatihan", "programs": "Program Pelatihan", "schedule": "<PERSON><PERSON><PERSON>", "employee": "<PERSON><PERSON><PERSON>", "course": "<PERSON><PERSON><PERSON>", "completionDate": "<PERSON><PERSON>", "expiryDate": "<PERSON><PERSON>", "instructor": "Instruktur", "certificate": "Ser<PERSON><PERSON><PERSON>", "refresher": "<PERSON><PERSON><PERSON><PERSON>", "overdue": "<PERSON><PERSON><PERSON><PERSON>"}, "dashboard": {"title": "Dasbor K3", "kpis": "<PERSON><PERSON><PERSON><PERSON>", "incidentRate": "<PERSON>g<PERSON>n", "daysWithoutIncident": "<PERSON>", "openActions": "<PERSON><PERSON><PERSON>", "overdueTraining": "<PERSON><PERSON><PERSON><PERSON>", "expiredPermits": "<PERSON><PERSON>", "riskAssessments": "<PERSON><PERSON><PERSON> R<PERSON>", "recentIncidents": "Insiden <PERSON>", "upcomingTraining": "<PERSON><PERSON><PERSON><PERSON>"}}