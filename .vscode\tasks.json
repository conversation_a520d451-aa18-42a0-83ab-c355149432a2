{"version": "2.0.0", "tasks": [{"label": "Run Full Stack", "dependsOn": ["<PERSON>", "Run <PERSON>end"], "group": {"kind": "build", "isDefault": true}, "problemMatcher": []}, {"label": "<PERSON>", "type": "shell", "command": "dotnet", "args": ["watch", "run", "--project", "DigitalHSE.Web"], "options": {"cwd": "${workspaceFolder}"}, "group": "build", "problemMatcher": "$msCompile"}, {"label": "Run <PERSON>end", "type": "shell", "command": "npm", "args": ["start"], "options": {"cwd": "${workspaceFolder}/DigitalHSE.Web/ClientApp"}, "group": "build", "problemMatcher": []}, {"label": "Build Solution", "type": "shell", "command": "dotnet", "args": ["build"], "options": {"cwd": "${workspaceFolder}"}, "group": "build", "problemMatcher": "$msCompile"}, {"label": "Test Solution", "type": "shell", "command": "dotnet", "args": ["test"], "options": {"cwd": "${workspaceFolder}"}, "group": "test", "problemMatcher": "$msCompile"}, {"label": "Install Frontend Dependencies", "type": "shell", "command": "npm", "args": ["install"], "options": {"cwd": "${workspaceFolder}/DigitalHSE.Web/ClientApp"}, "problemMatcher": []}]}