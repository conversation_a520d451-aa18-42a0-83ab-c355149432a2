"use strict";(self.webpackChunkdigitalhse_web=self.webpackChunkdigitalhse_web||[]).push([[481],{13019:(e,r,a)=>{a.d(r,{V:()=>d});var s=a(3035),l=a(9950),n=a(11942),c=a.n(n),t=a(69344),d=(0,l.forwardRef)((function(e,r){var a=e.children,n=e.as,c=void 0===n?"div":n,d=e.className,o=(0,s.Tt)(e,["children","as","className"]);return l.createElement(c,(0,s.Cl)({className:(0,t.A)("card-header",d)},o,{ref:r}),a)}));d.propTypes={as:c().elementType,children:c().node,className:c().string},d.displayName="CCardHeader"},30578:(e,r,a)=>{a.d(r,{E:()=>o});var s=a(3035),l=a(9950),n=a(11942),c=a.n(n),t=a(69344),d=a(3319),o=(0,l.forwardRef)((function(e,r){var a,n=e.children,c=e.className,d=e.color,o=e.textBgColor,i=e.textColor,h=(0,s.Tt)(e,["children","className","color","textBgColor","textColor"]);return l.createElement("div",(0,s.Cl)({className:(0,t.A)("card",(a={},a["bg-".concat(d)]=d,a["text-".concat(i)]=i,a["text-bg-".concat(o)]=o,a),c)},h,{ref:r}),n)}));o.propTypes={children:c().node,className:c().string,color:d.TX,textBgColor:d.TX,textColor:c().string},o.displayName="CCard"},63481:(e,r,a)=>{a.r(r),a.d(r,{default:()=>t});a(9950);var s=a(30578),l=a(13019),n=a(98114),c=a(44414);const t=()=>(0,c.jsxs)(s.E,{children:[(0,c.jsx)(l.V,{children:(0,c.jsx)("strong",{children:"Training Dashboard"})}),(0,c.jsx)(n.W,{children:(0,c.jsx)("p",{children:"Training overview - To be implemented"})})]})},98114:(e,r,a)=>{a.d(r,{W:()=>d});var s=a(3035),l=a(9950),n=a(11942),c=a.n(n),t=a(69344),d=(0,l.forwardRef)((function(e,r){var a=e.children,n=e.className,c=(0,s.Tt)(e,["children","className"]);return l.createElement("div",(0,s.Cl)({className:(0,t.A)("card-body",n)},c,{ref:r}),a)}));d.propTypes={children:c().node,className:c().string},d.displayName="CCardBody"}}]);