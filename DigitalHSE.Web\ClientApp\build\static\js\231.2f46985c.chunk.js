"use strict";(self.webpackChunkdigitalhse_web=self.webpackChunkdigitalhse_web||[]).push([[231],{13019:(e,r,a)=>{a.d(r,{V:()=>d});var l=a(3035),s=a(9950),c=a(11942),n=a.n(c),t=a(69344),d=(0,s.forwardRef)((function(e,r){var a=e.children,c=e.as,n=void 0===c?"div":c,d=e.className,o=(0,l.Tt)(e,["children","as","className"]);return s.createElement(n,(0,l.Cl)({className:(0,t.A)("card-header",d)},o,{ref:r}),a)}));d.propTypes={as:n().elementType,children:n().node,className:n().string},d.displayName="CCardHeader"},30578:(e,r,a)=>{a.d(r,{E:()=>o});var l=a(3035),s=a(9950),c=a(11942),n=a.n(c),t=a(69344),d=a(3319),o=(0,s.forwardRef)((function(e,r){var a,c=e.children,n=e.className,d=e.color,o=e.textBgColor,i=e.textColor,m=(0,l.Tt)(e,["children","className","color","textBgColor","textColor"]);return s.createElement("div",(0,l.Cl)({className:(0,t.A)("card",(a={},a["bg-".concat(d)]=d,a["text-".concat(i)]=i,a["text-bg-".concat(o)]=o,a),n)},m,{ref:r}),c)}));o.propTypes={children:n().node,className:n().string,color:d.TX,textBgColor:d.TX,textColor:n().string},o.displayName="CCard"},32231:(e,r,a)=>{a.r(r),a.d(r,{default:()=>t});a(9950);var l=a(30578),s=a(13019),c=a(98114),n=a(44414);const t=()=>(0,n.jsxs)(l.E,{children:[(0,n.jsx)(s.V,{children:(0,n.jsx)("strong",{children:"Compliance Dashboard"})}),(0,n.jsx)(c.W,{children:(0,n.jsx)("p",{children:"Compliance tracking - To be implemented"})})]})},98114:(e,r,a)=>{a.d(r,{W:()=>d});var l=a(3035),s=a(9950),c=a(11942),n=a.n(c),t=a(69344),d=(0,s.forwardRef)((function(e,r){var a=e.children,c=e.className,n=(0,l.Tt)(e,["children","className"]);return s.createElement("div",(0,l.Cl)({className:(0,t.A)("card-body",c)},n,{ref:r}),a)}));d.propTypes={children:n().node,className:n().string},d.displayName="CCardBody"}}]);