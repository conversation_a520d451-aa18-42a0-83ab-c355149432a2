"use strict";(self.webpackChunkdigitalhse_web=self.webpackChunkdigitalhse_web||[]).push([[151],{13019:(e,r,a)=>{a.d(r,{V:()=>t});var s=a(3035),l=a(9950),c=a(11942),n=a.n(c),d=a(69344),t=(0,l.forwardRef)((function(e,r){var a=e.children,c=e.as,n=void 0===c?"div":c,t=e.className,o=(0,s.Tt)(e,["children","as","className"]);return l.createElement(n,(0,s.Cl)({className:(0,d.A)("card-header",t)},o,{ref:r}),a)}));t.propTypes={as:n().elementType,children:n().node,className:n().string},t.displayName="CCardHeader"},19151:(e,r,a)=>{a.r(r),a.d(r,{default:()=>d});a(9950);var s=a(30578),l=a(13019),c=a(98114),n=a(44414);const d=()=>(0,n.jsxs)(s.E,{children:[(0,n.jsx)(l.V,{children:(0,n.jsx)("strong",{children:"Training Records"})}),(0,n.jsx)(c.W,{children:(0,n.jsx)("p",{children:"Training records - To be implemented"})})]})},30578:(e,r,a)=>{a.d(r,{E:()=>o});var s=a(3035),l=a(9950),c=a(11942),n=a.n(c),d=a(69344),t=a(3319),o=(0,l.forwardRef)((function(e,r){var a,c=e.children,n=e.className,t=e.color,o=e.textBgColor,i=e.textColor,m=(0,s.Tt)(e,["children","className","color","textBgColor","textColor"]);return l.createElement("div",(0,s.Cl)({className:(0,d.A)("card",(a={},a["bg-".concat(t)]=t,a["text-".concat(i)]=i,a["text-bg-".concat(o)]=o,a),n)},m,{ref:r}),c)}));o.propTypes={children:n().node,className:n().string,color:t.TX,textBgColor:t.TX,textColor:n().string},o.displayName="CCard"},98114:(e,r,a)=>{a.d(r,{W:()=>t});var s=a(3035),l=a(9950),c=a(11942),n=a.n(c),d=a(69344),t=(0,l.forwardRef)((function(e,r){var a=e.children,c=e.className,n=(0,s.Tt)(e,["children","className"]);return l.createElement("div",(0,s.Cl)({className:(0,d.A)("card-body",c)},n,{ref:r}),a)}));t.propTypes={children:n().node,className:n().string},t.displayName="CCardBody"}}]);