﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="FluentValidation" Version="11.9.1" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.9.1" />
    <PackageReference Include="Humanizer.Core" Version="2.14.1" />
	<PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
	<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="6.5.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.1.2" />
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\DigitalHSE.Domain\DigitalHSE.Domain.csproj" />
    <ProjectReference Include="..\DigitalHSE.Resources\DigitalHSE.Resources.csproj" />
  </ItemGroup>

</Project>
