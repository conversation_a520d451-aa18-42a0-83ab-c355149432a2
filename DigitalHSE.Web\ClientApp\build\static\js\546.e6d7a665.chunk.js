"use strict";(self.webpackChunkdigitalhse_web=self.webpackChunkdigitalhse_web||[]).push([[546],{91546:(e,s,i)=>{i.r(s),i.d(s,{default:()=>re});var n=i(89379),a=i(9950),l=i(28429),t=i(67818),r=i(94063),c=i(53560),d=i(52684),o=i(71398),m=i(39696),h=i(9134),x=i(30578),j=i(13019),v=i(3628),u=i(61114),g=i(98114),p=i(38290),y=i(71028),N=i(17831),f=i(45728),b=i(15170),A=i(97098),w=i(40121),C=i(8236),I=i(33652),T=i(67111),S=i(63898),E=i(14778),k=i(17882),P=i(72421),z=i(83458),W=i(53612),D=i(3380),B=i(48297),M=i(48246),F=i(68852),U=i(49468),R=i(36617),Q=i(8572),O=i(58756),V=i(60538),L=i(78402),$=i(85042),H=i(82699),J=i(81949),q=i(2977),K=i(6842),Z=i(30871),_=i(5754),X=i(30169),Y=i(25152),G=i(88859),ee=i(31356),se=i(8134),ie=i(3526),ne=i(62293),ae=i(81974),le=i(65321),te=i(44414);const re=()=>{const{id:e}=(0,l.g)(),{t:s}=(0,t.Bd)(["hse","common"]),[i,re]=(0,a.useState)(null),[ce,de]=(0,a.useState)(!0),[oe,me]=(0,a.useState)(!1),[he,xe]=(0,a.useState)("overview"),[je,ve]=(0,a.useState)(!1),[ue,ge]=(0,a.useState)(!1),[pe,ye]=(0,a.useState)({name:"",role:"",department:"",email:""});(0,a.useEffect)((()=>{(async()=>{de(!0),await new Promise((e=>setTimeout(e,1e3))),re({id:1,incidentId:parseInt(e||"1"),incidentNumber:"INC-20240115-1234",incidentTitle:"Student injured during PE class",leadInvestigator:"Dr. Ahmad Wijaya",teamMembers:[{name:"Dr. Ahmad Wijaya",role:"Lead Investigator",department:"Safety Department",email:"<EMAIL>"},{name:"Coach Sarah Wilson",role:"Witness",department:"Physical Education",email:"<EMAIL>"},{name:"Nurse Linda Sari",role:"Medical Expert",department:"Health Services",email:"<EMAIL>"}],startDate:"2024-01-15T11:00:00",targetCompletionDate:"2024-01-22T17:00:00",status:"InProgress",progressPercentage:65,methodology:"5 Whys",initialFindings:"Student fell while attempting to shoot basketball. No immediate safety hazards observed in the gymnasium.",interviewNotes:"Student reported feeling dizzy before the incident. Coach confirmed proper warm-up was conducted.",evidenceCollected:"Photos of gymnasium floor, basketball court layout, medical examination report",witnessStatements:"Three students witnessed the incident. All confirmed student appeared unsteady before falling.",rootCauses:["Student did not report feeling unwell before activity","No pre-activity health check protocol in place"],contributingFactors:["Student skipped breakfast","High temperature in gymnasium","Intense warm-up session"],systemicIssues:"Lack of mandatory health declaration before physical activities",immediateActions:"Student received immediate first aid and medical attention",shortTermActions:"Implement pre-activity health check for all PE classes",longTermActions:"Develop comprehensive student health monitoring system",preventiveMeasures:"Regular health screenings, improved gymnasium ventilation, mandatory breakfast program information"}),de(!1)})()}),[e]);const Ne=e=>e>=80?"success":e>=60?"info":e>=40?"warning":"danger";return ce?(0,te.jsx)(d.s,{children:(0,te.jsxs)(o.U,{xs:12,className:"text-center py-5",children:[(0,te.jsx)(m.J,{color:"primary"}),(0,te.jsx)("div",{className:"mt-2",children:"Loading investigation details..."})]})}):i?(0,te.jsxs)(d.s,{children:[(0,te.jsx)(o.U,{xs:12,className:"mb-4",children:(0,te.jsxs)(x.E,{children:[(0,te.jsxs)(j.V,{className:"d-flex justify-content-between align-items-center",children:[(0,te.jsxs)("div",{children:[(0,te.jsxs)("h4",{className:"mb-1",children:[(0,te.jsx)(J.Ay,{icon:K.B,className:"me-2"}),"Investigation: ",i.incidentNumber]}),(0,te.jsx)("p",{className:"text-medium-emphasis mb-0",children:i.incidentTitle})]}),(0,te.jsxs)("div",{className:"d-flex gap-2",children:[(0,te.jsx)(v.$,{color:(e=>{switch(e){case"Assigned":return"primary";case"InProgress":return"warning";case"PendingApproval":return"info";case"Completed":return"success";case"OnHold":default:return"secondary";case"Cancelled":return"danger"}})(i.status),className:"px-3 py-2",children:i.status}),(0,te.jsx)(u.Q,{color:"primary",size:"sm",onClick:async()=>{me(!0);try{await new Promise((e=>setTimeout(e,1e3))),addToast((0,te.jsx)(r.J,{color:"success",children:(0,te.jsx)(c.B,{children:"Investigation progress saved successfully!"})}))}catch(e){addToast((0,te.jsx)(r.J,{color:"danger",children:(0,te.jsx)(c.B,{children:"Failed to save investigation progress."})}))}finally{me(!1)}},disabled:oe,children:oe?(0,te.jsxs)(te.Fragment,{children:[(0,te.jsx)(m.J,{size:"sm",className:"me-2"}),"Saving..."]}):(0,te.jsxs)(te.Fragment,{children:[(0,te.jsx)(J.Ay,{icon:Z.M,className:"me-2"}),"Save Progress"]})})]})]}),(0,te.jsx)(g.W,{children:(0,te.jsxs)(d.s,{children:[(0,te.jsx)(o.U,{md:3,children:(0,te.jsxs)("div",{className:"border-end pe-3",children:[(0,te.jsx)("div",{className:"small text-medium-emphasis",children:"Lead Investigator"}),(0,te.jsx)("div",{className:"fw-semibold",children:i.leadInvestigator})]})}),(0,te.jsx)(o.U,{md:3,children:(0,te.jsxs)("div",{className:"border-end pe-3",children:[(0,te.jsx)("div",{className:"small text-medium-emphasis",children:"Start Date"}),(0,te.jsx)("div",{className:"fw-semibold",children:new Date(i.startDate).toLocaleDateString()})]})}),(0,te.jsx)(o.U,{md:3,children:(0,te.jsxs)("div",{className:"border-end pe-3",children:[(0,te.jsx)("div",{className:"small text-medium-emphasis",children:"Target Completion"}),(0,te.jsx)("div",{className:"fw-semibold",children:new Date(i.targetCompletionDate).toLocaleDateString()})]})}),(0,te.jsx)(o.U,{md:3,children:(0,te.jsxs)("div",{children:[(0,te.jsx)("div",{className:"small text-medium-emphasis",children:"Progress"}),(0,te.jsxs)("div",{className:"d-flex align-items-center",children:[(0,te.jsx)(p.f,{value:i.progressPercentage,color:Ne(i.progressPercentage),className:"flex-grow-1 me-2"}),(0,te.jsxs)("span",{className:"fw-semibold",children:[i.progressPercentage,"%"]})]})]})})]})})]})}),(0,te.jsx)(o.U,{xs:12,children:(0,te.jsx)(x.E,{children:(0,te.jsxs)(g.W,{children:[(0,te.jsxs)(y.b,{variant:"tabs",role:"tablist",children:[(0,te.jsx)(N.g,{children:(0,te.jsxs)(f.H,{active:"overview"===he,onClick:()=>xe("overview"),style:{cursor:"pointer"},children:[(0,te.jsx)(J.Ay,{icon:_.x,className:"me-2"}),"Overview"]})}),(0,te.jsx)(N.g,{children:(0,te.jsxs)(f.H,{active:"team"===he,onClick:()=>xe("team"),style:{cursor:"pointer"},children:[(0,te.jsx)(J.Ay,{icon:X.g,className:"me-2"}),"Investigation Team"]})}),(0,te.jsx)(N.g,{children:(0,te.jsxs)(f.H,{active:"evidence"===he,onClick:()=>xe("evidence"),style:{cursor:"pointer"},children:[(0,te.jsx)(J.Ay,{icon:Y.T,className:"me-2"}),"Evidence & Findings"]})}),(0,te.jsx)(N.g,{children:(0,te.jsxs)(f.H,{active:"rca"===he,onClick:()=>xe("rca"),style:{cursor:"pointer"},children:[(0,te.jsx)(J.Ay,{icon:K.B,className:"me-2"}),"Root Cause Analysis"]})}),(0,te.jsx)(N.g,{children:(0,te.jsxs)(f.H,{active:"actions"===he,onClick:()=>xe("actions"),style:{cursor:"pointer"},children:[(0,te.jsx)(J.Ay,{icon:G.O,className:"me-2"}),"Actions & Recommendations"]})})]}),(0,te.jsxs)(b.e,{children:[(0,te.jsx)(A.x,{visible:"overview"===he,children:(0,te.jsxs)(d.s,{children:[(0,te.jsx)(o.U,{md:6,children:(0,te.jsxs)(x.E,{className:"h-100",children:[(0,te.jsx)(j.V,{children:(0,te.jsx)("strong",{children:"Investigation Timeline"})}),(0,te.jsxs)(g.W,{children:[(0,te.jsxs)("div",{className:"d-flex mb-3",children:[(0,te.jsx)("div",{className:"flex-shrink-0",children:(0,te.jsx)("div",{className:"bg-primary rounded-circle d-flex align-items-center justify-content-center",style:{width:"32px",height:"32px"},children:(0,te.jsx)(J.Ay,{icon:ee.$,className:"text-white",size:"sm"})})}),(0,te.jsxs)("div",{className:"ms-3",children:[(0,te.jsx)("div",{className:"fw-semibold",children:"Investigation Started"}),(0,te.jsx)("div",{className:"text-medium-emphasis small",children:new Date(i.startDate).toLocaleString()})]})]}),(0,te.jsxs)("div",{className:"d-flex mb-3",children:[(0,te.jsx)("div",{className:"flex-shrink-0",children:(0,te.jsx)("div",{className:"bg-".concat(Ne(i.progressPercentage)," rounded-circle d-flex align-items-center justify-content-center"),style:{width:"32px",height:"32px"},children:(0,te.jsx)(J.Ay,{icon:se.V,className:"text-white",size:"sm"})})}),(0,te.jsxs)("div",{className:"ms-3",children:[(0,te.jsx)("div",{className:"fw-semibold",children:"Current Progress"}),(0,te.jsxs)("div",{className:"text-medium-emphasis small",children:[i.progressPercentage,"% completed"]})]})]}),(0,te.jsxs)("div",{className:"d-flex",children:[(0,te.jsx)("div",{className:"flex-shrink-0",children:(0,te.jsx)("div",{className:"bg-secondary rounded-circle d-flex align-items-center justify-content-center",style:{width:"32px",height:"32px"},children:(0,te.jsx)(J.Ay,{icon:q.j,className:"text-white",size:"sm"})})}),(0,te.jsxs)("div",{className:"ms-3",children:[(0,te.jsx)("div",{className:"fw-semibold",children:"Target Completion"}),(0,te.jsx)("div",{className:"text-medium-emphasis small",children:new Date(i.targetCompletionDate).toLocaleString()})]})]})]})]})}),(0,te.jsx)(o.U,{md:6,children:(0,te.jsxs)(x.E,{className:"h-100",children:[(0,te.jsx)(j.V,{children:(0,te.jsx)("strong",{children:"Investigation Summary"})}),(0,te.jsxs)(g.W,{children:[(0,te.jsxs)("div",{className:"mb-3",children:[(0,te.jsx)("div",{className:"small text-medium-emphasis",children:"Methodology"}),(0,te.jsx)(v.$,{color:"info",className:"px-2 py-1",children:i.methodology})]}),(0,te.jsxs)("div",{className:"mb-3",children:[(0,te.jsx)("div",{className:"small text-medium-emphasis",children:"Team Size"}),(0,te.jsxs)("div",{className:"fw-semibold",children:[i.teamMembers.length," members"]})]}),(0,te.jsxs)("div",{className:"mb-3",children:[(0,te.jsx)("div",{className:"small text-medium-emphasis",children:"Root Causes Identified"}),(0,te.jsx)("div",{className:"fw-semibold",children:i.rootCauses.length})]}),(0,te.jsxs)("div",{children:[(0,te.jsx)("div",{className:"small text-medium-emphasis",children:"Contributing Factors"}),(0,te.jsx)("div",{className:"fw-semibold",children:i.contributingFactors.length})]})]})]})})]})}),(0,te.jsxs)(A.x,{visible:"team"===he,children:[(0,te.jsxs)("div",{className:"d-flex justify-content-between align-items-center mb-3",children:[(0,te.jsx)("h5",{children:"Investigation Team"}),(0,te.jsxs)(u.Q,{color:"primary",size:"sm",onClick:()=>ve(!0),children:[(0,te.jsx)(J.Ay,{icon:ie.x,className:"me-2"}),"Add Team Member"]})]}),(0,te.jsxs)(w._,{hover:!0,responsive:!0,children:[(0,te.jsx)(C.w,{children:(0,te.jsxs)(I.Y,{children:[(0,te.jsx)(T.$,{children:"Name"}),(0,te.jsx)(T.$,{children:"Role"}),(0,te.jsx)(T.$,{children:"Department"}),(0,te.jsx)(T.$,{children:"Email"}),(0,te.jsx)(T.$,{children:"Actions"})]})}),(0,te.jsx)(S.C,{children:i.teamMembers.map(((e,s)=>(0,te.jsxs)(I.Y,{children:[(0,te.jsx)(E.c,{children:(0,te.jsxs)("div",{className:"d-flex align-items-center",children:[(0,te.jsx)(J.Ay,{icon:ne.o,className:"me-2"}),e.name]})}),(0,te.jsx)(E.c,{children:(0,te.jsx)(v.$,{color:"secondary",children:e.role})}),(0,te.jsx)(E.c,{children:e.department}),(0,te.jsx)(E.c,{children:e.email}),(0,te.jsx)(E.c,{children:(0,te.jsx)(u.Q,{color:"danger",variant:"ghost",size:"sm",children:(0,te.jsx)(J.Ay,{icon:ae.Z})})})]},s)))})]})]}),(0,te.jsx)(A.x,{visible:"evidence"===he,children:(0,te.jsxs)(k.E,{flush:!0,children:[(0,te.jsxs)(P.l,{itemKey:"initial",children:[(0,te.jsx)(z.B,{children:"Initial Findings"}),(0,te.jsx)(W.i,{children:(0,te.jsx)(D.I,{rows:4,value:i.initialFindings,onChange:e=>re((0,n.A)((0,n.A)({},i),{},{initialFindings:e.target.value})),placeholder:"Document initial observations and findings..."})})]}),(0,te.jsxs)(P.l,{itemKey:"interviews",children:[(0,te.jsx)(z.B,{children:"Interview Notes"}),(0,te.jsx)(W.i,{children:(0,te.jsx)(D.I,{rows:6,value:i.interviewNotes,onChange:e=>re((0,n.A)((0,n.A)({},i),{},{interviewNotes:e.target.value})),placeholder:"Record interview notes with witnesses, staff, and other relevant parties..."})})]}),(0,te.jsxs)(P.l,{itemKey:"evidence",children:[(0,te.jsx)(z.B,{children:"Physical Evidence"}),(0,te.jsxs)(W.i,{children:[(0,te.jsx)(D.I,{rows:4,value:i.evidenceCollected,onChange:e=>re((0,n.A)((0,n.A)({},i),{},{evidenceCollected:e.target.value})),placeholder:"Document physical evidence collected, photos, videos, documents..."}),(0,te.jsxs)("div",{className:"mt-3",children:[(0,te.jsxs)(u.Q,{color:"secondary",variant:"outline",size:"sm",className:"me-2",children:[(0,te.jsx)(J.Ay,{icon:le.u,className:"me-1"}),"Upload Photos"]}),(0,te.jsxs)(u.Q,{color:"secondary",variant:"outline",size:"sm",children:[(0,te.jsx)(J.Ay,{icon:Y.T,className:"me-1"}),"Upload Documents"]})]})]})]}),(0,te.jsxs)(P.l,{itemKey:"witnesses",children:[(0,te.jsx)(z.B,{children:"Witness Statements"}),(0,te.jsx)(W.i,{children:(0,te.jsx)(D.I,{rows:6,value:i.witnessStatements,onChange:e=>re((0,n.A)((0,n.A)({},i),{},{witnessStatements:e.target.value})),placeholder:"Record detailed witness statements and testimonies..."})})]})]})}),(0,te.jsxs)(A.x,{visible:"rca"===he,children:[(0,te.jsxs)("div",{className:"d-flex justify-content-between align-items-center mb-3",children:[(0,te.jsx)("h5",{children:"Root Cause Analysis"}),(0,te.jsxs)(u.Q,{color:"info",size:"sm",onClick:()=>ge(!0),children:[(0,te.jsx)(J.Ay,{icon:cilBullhorn,className:"me-2"}),"RCA Assistant"]})]}),(0,te.jsxs)(d.s,{children:[(0,te.jsx)(o.U,{md:6,children:(0,te.jsxs)(x.E,{className:"mb-3",children:[(0,te.jsx)(j.V,{children:(0,te.jsx)("strong",{children:"Root Causes"})}),(0,te.jsxs)(g.W,{children:[(0,te.jsx)(B.X,{flush:!0,children:i.rootCauses.map(((e,s)=>(0,te.jsxs)(M.y,{className:"d-flex justify-content-between align-items-center",children:[e,(0,te.jsx)(u.Q,{color:"danger",variant:"ghost",size:"sm",children:(0,te.jsx)(J.Ay,{icon:ae.Z})})]},s)))}),(0,te.jsxs)("div",{className:"mt-3",children:[(0,te.jsx)(F.O,{placeholder:"Add new root cause..."}),(0,te.jsxs)(u.Q,{color:"primary",size:"sm",className:"mt-2",children:[(0,te.jsx)(J.Ay,{icon:ie.x,className:"me-1"}),"Add"]})]})]})]})}),(0,te.jsx)(o.U,{md:6,children:(0,te.jsxs)(x.E,{className:"mb-3",children:[(0,te.jsx)(j.V,{children:(0,te.jsx)("strong",{children:"Contributing Factors"})}),(0,te.jsxs)(g.W,{children:[(0,te.jsx)(B.X,{flush:!0,children:i.contributingFactors.map(((e,s)=>(0,te.jsxs)(M.y,{className:"d-flex justify-content-between align-items-center",children:[e,(0,te.jsx)(u.Q,{color:"danger",variant:"ghost",size:"sm",children:(0,te.jsx)(J.Ay,{icon:ae.Z})})]},s)))}),(0,te.jsxs)("div",{className:"mt-3",children:[(0,te.jsx)(F.O,{placeholder:"Add contributing factor..."}),(0,te.jsxs)(u.Q,{color:"primary",size:"sm",className:"mt-2",children:[(0,te.jsx)(J.Ay,{icon:ie.x,className:"me-1"}),"Add"]})]})]})]})})]}),(0,te.jsxs)(x.E,{children:[(0,te.jsx)(j.V,{children:(0,te.jsx)("strong",{children:"Systemic Issues"})}),(0,te.jsx)(g.W,{children:(0,te.jsx)(D.I,{rows:4,value:i.systemicIssues,onChange:e=>re((0,n.A)((0,n.A)({},i),{},{systemicIssues:e.target.value})),placeholder:"Describe any systemic or organizational issues that contributed to the incident..."})})]})]}),(0,te.jsxs)(A.x,{visible:"actions"===he,children:[(0,te.jsxs)(d.s,{children:[(0,te.jsxs)(o.U,{md:6,children:[(0,te.jsxs)(x.E,{className:"mb-3",children:[(0,te.jsx)(j.V,{children:(0,te.jsx)("strong",{children:"Immediate Actions"})}),(0,te.jsx)(g.W,{children:(0,te.jsx)(D.I,{rows:4,value:i.immediateActions,onChange:e=>re((0,n.A)((0,n.A)({},i),{},{immediateActions:e.target.value})),placeholder:"Actions taken immediately after the incident..."})})]}),(0,te.jsxs)(x.E,{children:[(0,te.jsx)(j.V,{children:(0,te.jsx)("strong",{children:"Short-term Actions (1-30 days)"})}),(0,te.jsx)(g.W,{children:(0,te.jsx)(D.I,{rows:4,value:i.shortTermActions,onChange:e=>re((0,n.A)((0,n.A)({},i),{},{shortTermActions:e.target.value})),placeholder:"Actions to be implemented within 30 days..."})})]})]}),(0,te.jsxs)(o.U,{md:6,children:[(0,te.jsxs)(x.E,{className:"mb-3",children:[(0,te.jsx)(j.V,{children:(0,te.jsx)("strong",{children:"Long-term Actions (30+ days)"})}),(0,te.jsx)(g.W,{children:(0,te.jsx)(D.I,{rows:4,value:i.longTermActions,onChange:e=>re((0,n.A)((0,n.A)({},i),{},{longTermActions:e.target.value})),placeholder:"Strategic actions for long-term improvement..."})})]}),(0,te.jsxs)(x.E,{children:[(0,te.jsx)(j.V,{children:(0,te.jsx)("strong",{children:"Preventive Measures"})}),(0,te.jsx)(g.W,{children:(0,te.jsx)(D.I,{rows:4,value:i.preventiveMeasures,onChange:e=>re((0,n.A)((0,n.A)({},i),{},{preventiveMeasures:e.target.value})),placeholder:"Measures to prevent similar incidents in the future..."})})]})]})]}),"Completed"!==i.status&&(0,te.jsx)("div",{className:"text-end mt-4",children:(0,te.jsx)(u.Q,{color:"success",size:"lg",onClick:async()=>{if(i){me(!0);try{await new Promise((e=>setTimeout(e,1500))),re((0,n.A)((0,n.A)({},i),{},{status:"Completed",progressPercentage:100,actualCompletionDate:(new Date).toISOString()})),addToast((0,te.jsx)(r.J,{color:"success",children:(0,te.jsx)(c.B,{children:(0,te.jsxs)("div",{className:"d-flex align-items-center",children:[(0,te.jsx)(J.Ay,{icon:q.j,className:"me-2"}),"Investigation completed successfully! Report is ready for review."]})})}))}catch(e){addToast((0,te.jsx)(r.J,{color:"danger",children:(0,te.jsx)(c.B,{children:"Failed to complete investigation."})}))}finally{me(!1)}}},disabled:oe||i.progressPercentage<80,children:oe?(0,te.jsxs)(te.Fragment,{children:[(0,te.jsx)(m.J,{size:"sm",className:"me-2"}),"Completing..."]}):(0,te.jsxs)(te.Fragment,{children:[(0,te.jsx)(J.Ay,{icon:q.j,className:"me-2"}),"Complete Investigation"]})})})]})]})]})})}),(0,te.jsxs)(U.z,{visible:je,onClose:()=>ve(!1),children:[(0,te.jsx)(R.E,{children:(0,te.jsx)(Q.l,{children:"Add Team Member"})}),(0,te.jsx)(O.T,{children:(0,te.jsxs)(V.q,{children:[(0,te.jsxs)("div",{className:"mb-3",children:[(0,te.jsx)(L.A,{children:"Name"}),(0,te.jsx)(F.O,{value:pe.name,onChange:e=>ye((0,n.A)((0,n.A)({},pe),{},{name:e.target.value})),placeholder:"Enter full name"})]}),(0,te.jsxs)("div",{className:"mb-3",children:[(0,te.jsx)(L.A,{children:"Role"}),(0,te.jsxs)($.M,{value:pe.role,onChange:e=>ye((0,n.A)((0,n.A)({},pe),{},{role:e.target.value})),children:[(0,te.jsx)("option",{value:"",children:"Select role..."}),(0,te.jsx)("option",{value:"Investigator",children:"Investigator"}),(0,te.jsx)("option",{value:"Technical Expert",children:"Technical Expert"}),(0,te.jsx)("option",{value:"Witness",children:"Witness"}),(0,te.jsx)("option",{value:"Medical Expert",children:"Medical Expert"}),(0,te.jsx)("option",{value:"Safety Officer",children:"Safety Officer"})]})]}),(0,te.jsxs)("div",{className:"mb-3",children:[(0,te.jsx)(L.A,{children:"Department"}),(0,te.jsx)(F.O,{value:pe.department,onChange:e=>ye((0,n.A)((0,n.A)({},pe),{},{department:e.target.value})),placeholder:"Enter department"})]}),(0,te.jsxs)("div",{className:"mb-3",children:[(0,te.jsx)(L.A,{children:"Email"}),(0,te.jsx)(F.O,{type:"email",value:pe.email,onChange:e=>ye((0,n.A)((0,n.A)({},pe),{},{email:e.target.value})),placeholder:"Enter email address"})]})]})}),(0,te.jsxs)(H.I,{children:[(0,te.jsx)(u.Q,{color:"secondary",onClick:()=>ve(!1),children:"Cancel"}),(0,te.jsx)(u.Q,{color:"primary",onClick:()=>{if(!i)return;const e=[...i.teamMembers,pe];re((0,n.A)((0,n.A)({},i),{},{teamMembers:e})),ye({name:"",role:"",department:"",email:""}),ve(!1),addToast((0,te.jsx)(r.J,{color:"success",children:(0,te.jsx)(c.B,{children:"Team member added successfully!"})}))},children:"Add Member"})]})]}),(0,te.jsxs)(U.z,{size:"lg",visible:ue,onClose:()=>ge(!1),children:[(0,te.jsx)(R.E,{children:(0,te.jsx)(Q.l,{children:"Root Cause Analysis Assistant"})}),(0,te.jsxs)(O.T,{children:[(0,te.jsx)("p",{className:"text-medium-emphasis mb-3",children:"Choose an RCA methodology to guide your analysis process."}),(0,te.jsx)(d.s,{children:[{name:"5 Whys",description:"Iterative interrogative technique to explore cause-and-effect relationships",template:"Why did this happen?\n1. Why: \n2. Why: \n3. Why: \n4. Why: \n5. Why: "},{name:"Fishbone/Ishikawa",description:"Categorizes potential causes to identify root causes",template:"Categories:\n- People: \n- Process: \n- Equipment: \n- Environment: \n- Materials: \n- Management: "},{name:"Fault Tree Analysis",description:"Top-down approach using Boolean logic to analyze system failures",template:"Top Event: \nImmediate Causes:\n- Cause 1: \n- Cause 2: \nBasic Events:\n- Event 1: \n- Event 2: "},{name:"Bow-Tie Analysis",description:"Risk evaluation method combining fault tree and event tree analysis",template:"Hazard: \nThreat: \nTop Event: \nPrevention Barriers:\n- Barrier 1: \n- Barrier 2: \nConsequences:\n- Consequence 1: \n- Consequence 2: \nProtection Barriers:\n- Barrier 1: \n- Barrier 2: "}].map(((e,s)=>(0,te.jsx)(o.U,{md:6,className:"mb-3",children:(0,te.jsx)(x.E,{className:"h-100 cursor-pointer",onClick:()=>{re((0,n.A)((0,n.A)({},i),{},{methodology:e.name})),ge(!1)},children:(0,te.jsxs)(g.W,{children:[(0,te.jsx)("h6",{children:e.name}),(0,te.jsx)("p",{className:"text-medium-emphasis small",children:e.description}),(0,te.jsx)("div",{className:"mt-2",children:(0,te.jsx)(v.$,{color:"info",children:"Template Available"})})]})})},s)))})]}),(0,te.jsx)(H.I,{children:(0,te.jsx)(u.Q,{color:"secondary",onClick:()=>ge(!1),children:"Close"})})]})]}):(0,te.jsx)(d.s,{children:(0,te.jsx)(o.U,{xs:12,children:(0,te.jsx)(h.k,{color:"danger",children:"Investigation not found."})})})}}}]);