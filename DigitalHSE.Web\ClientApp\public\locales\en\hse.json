{"incidents": {"title": "Incident Management", "create": "Report New Incident", "list": "Incident List", "details": "Incident Details", "number": "Incident Number", "type": "Incident Type", "severity": {"minor": "Minor", "low": "Low", "moderate": "Moderate", "major": "Major", "critical": "Critical"}, "status": {"reported": "Reported", "investigating": "Under Investigation", "actionRequired": "Action Required", "closed": "Closed"}, "location": "Location", "description": "Description", "reportedBy": "Reported By", "reportedDate": "Reported Date", "incidentDate": "Incident Date", "department": "Department", "injuredPersons": "Injured Persons", "witnessNames": "Witness Names", "immediateActions": "Immediate Actions Taken", "rootCause": "Root Cause Analysis", "correctiveActions": "Corrective Actions", "preventiveActions": "Preventive Actions", "regulatory": {"bpjsReporting": "BPJS Reporting Required", "ministryReporting": "Ministry Reporting Required", "deadline": "Regulatory Deadline", "bpjsReference": "BPJS Reference Number", "ministryReference": "Ministry Reference Number"}, "types": {"nearmiss": "Near Miss", "firstaid": "First Aid", "medical": "Medical Treatment", "losttime": "Lost Time Injury", "property": "Property Damage", "environmental": "Environmental Incident", "studentInjury": "Student Injury", "staffInjury": "Staff Injury", "teacherInjury": "Teacher Injury", "playgroundAccident": "Playground Accident", "sportsInjury": "Sports Injury", "laboratoryAccident": "Laboratory Accident", "fieldTripIncident": "Field Trip Incident", "behavioralIncident": "Behavioral Incident", "bullyingIncident": "Bullying Incident", "securityIncident": "Security Incident", "foodSafetyIncident": "Food Safety Incident", "transportationIncident": "Transportation Incident", "fire": "Fire", "propertyDamage": "Property Damage", "nearMiss": "Near Miss", "other": "Other"}}, "riskAssessments": {"title": "Risk Assessment", "create": "Create Risk Assessment", "list": "Risk Assessment List", "details": "Risk Assessment Details", "activity": "Activity/Process", "hazards": "Identified Hazards", "riskLevel": "Risk Level", "controlMeasures": "Control Measures", "residualRisk": "Residual Risk", "reviewer": "Reviewed By", "approver": "Approved By", "validUntil": "<PERSON>id <PERSON>", "riskMatrix": "Risk Matrix", "likelihood": "Likelihood", "consequence": "Consequence"}, "permits": {"title": "Work Permits", "create": "Create Work Permit", "list": "Permit List", "details": "Permit Details", "type": "Permit Type", "workDescription": "Work Description", "location": "Work Location", "startDate": "Start Date", "endDate": "End Date", "contractor": "Contractor", "supervisor": "Supervisor", "safetyMeasures": "Safety Measures", "equipmentRequired": "Required Equipment", "approvals": "Approvals Required", "status": {"pending": "Pending Approval", "approved": "Approved", "active": "Active", "completed": "Completed", "cancelled": "Cancelled", "expired": "Expired"}, "types": {"hotwork": "Hot Work", "confinedspace": "Confined Space", "electrical": "Electrical Work", "excavation": "Excavation", "height": "Work at Height", "general": "General Work"}}, "compliance": {"title": "Compliance Management", "items": "Compliance Items", "requirements": "Requirements", "status": "Compliance Status", "dueDate": "Due Date", "lastAudit": "Last Audit", "nextAudit": "Next Audit", "responsible": "Responsible Person", "evidence": "Evidence/Documentation", "nonCompliances": "Non-Compliances", "correctiveActions": "Corrective Actions"}, "training": {"title": "HSE Training", "records": "Training Records", "programs": "Training Programs", "schedule": "Training Schedule", "employee": "Employee", "course": "Course", "completionDate": "Completion Date", "expiryDate": "Expiry Date", "instructor": "<PERSON><PERSON><PERSON><PERSON>", "certificate": "Certificate", "refresher": "Refresher Required", "overdue": "Overdue Training"}, "dashboard": {"title": "HSE Dashboard", "kpis": "Key Performance Indicators", "incidentRate": "Incident Rate", "daysWithoutIncident": "Days Without Lost Time Incident", "openActions": "Open Actions", "overdueTraining": "Overdue Training", "expiredPermits": "Expired Permits", "riskAssessments": "Risk Assessments Due", "recentIncidents": "Recent Incidents", "upcomingTraining": "Upcoming Training"}}