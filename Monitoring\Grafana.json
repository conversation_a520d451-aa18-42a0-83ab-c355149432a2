{"annotations": {"list": []}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": null, "iteration": 1, "links": [], "panels": [{"datasource": "prometheus", "fieldConfig": {"defaults": {"custom": {}, "mappings": []}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 0}, "id": 1, "options": {"legend": {"displayMode": "list", "placement": "right"}, "tooltip": {"mode": "single"}}, "targets": [{"expr": "sum(http_requests_received_total) by (endpoint)", "interval": "1m", "legendFormat": "{{ endpoint }}", "refId": "A"}], "title": "Endpoints", "type": "timeseries"}, {"datasource": "prometheus", "fieldConfig": {"defaults": {"custom": {}, "mappings": []}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 0}, "id": 2, "options": {"legend": {"displayMode": "list", "placement": "right"}, "tooltip": {"mode": "single"}}, "targets": [{"expr": "sum(http_requests_received_total) by (code)", "interval": "1m", "legendFormat": "{{ code }}", "refId": "A"}], "title": "Status Codes", "type": "timeseries"}, {"datasource": "prometheus", "fieldConfig": {"defaults": {"custom": {}, "mappings": []}, "overrides": []}, "gridPos": {"h": 10, "w": 6, "x": 0, "y": 10}, "id": 3, "options": {"legend": {"displayMode": "list", "placement": "right"}, "pieType": "donut", "showLabels": false, "tooltip": {"mode": "single"}}, "targets": [{"expr": "sum(http_requests_received_total{code=\"200\"}) / sum(http_requests_received_total) * 100", "interval": "1m", "legendFormat": "Success", "refId": "A"}, {"expr": "(sum(http_requests_received_total) - sum(http_requests_received_total{code=\"200\"})) / sum(http_requests_received_total) * 100", "interval": "1m", "legendFormat": "Fail", "refId": "B"}], "title": "Success Rate", "type": "grafana-piechart-panel"}], "refresh": "1m", "schemaVersion": 30, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "30m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "HTTP Requests Monitoring", "uid": null, "version": 1}