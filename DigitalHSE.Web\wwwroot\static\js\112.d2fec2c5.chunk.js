"use strict";(self.webpackChunkdigitalhse_web=self.webpackChunkdigitalhse_web||[]).push([[112],{79894:(e,s,i)=>{i.d(s,{X:()=>c});var n=i(82932);const c={async get(e){try{return{success:!0,data:(await n.Ay.get(e)).data}}catch(s){return console.error("API GET Error:",s),{success:!1,data:null,message:"Request failed"}}},async post(e,s,i){try{return{success:!0,data:(await n.Ay.post(e,s,i)).data}}catch(c){return console.error("API POST Error:",c),{success:!1,data:null,message:"Request failed"}}},async put(e,s){try{return{success:!0,data:(await n.Ay.put(e,s)).data}}catch(i){return console.error("API PUT Error:",i),{success:!1,data:null,message:"Request failed"}}},async delete(e){try{return{success:!0,data:(await n.Ay.delete(e)).data}}catch(s){return console.error("API DELETE Error:",s),{success:!1,data:null,message:"Request failed"}}}}},82112:(e,s,i)=>{i.r(s),i.d(s,{default:()=>We});var n=i(9950),c=i(64771),t=i(39696),r=i(61114),l=i(67583),a=i(35642),d=i(5617),o=i(1093),m=i(40565),h=i(52684),x=i(71398),j=i(19124),u=i(9134),y=i(30578),g=i(13019),p=i(98114),f=i(48297),N=i(48246),v=i(3628),b=i(40121),A=i(8236),C=i(33652),w=i(67111),D=i(63898),H=i(14778),M=i(38290),k=i(50025),S=i(17882),V=i(72421),P=i(83458),R=i(53612),E=i(72922),T=i(3035),U=i(11942),$=i.n(U),I=i(69344),L=(0,n.forwardRef)((function(e,s){var i,c=e.align,t=e.className,r=e.fluid,l=e.rounded,a=e.thumbnail,d=(0,T.Tt)(e,["align","className","fluid","rounded","thumbnail"]);return n.createElement("img",(0,T.Cl)({className:(0,I.A)((i={},i["float-".concat(c)]=c&&("start"===c||"end"===c),i["d-block mx-auto"]=c&&"center"===c,i["img-fluid"]=r,i.rounded=l,i["img-thumbnail"]=a,i),t)||void 0},d,{ref:s}))}));L.propTypes={align:$().oneOf(["start","center","end"]),className:$().string,fluid:$().bool,rounded:$().bool,thumbnail:$().bool},L.displayName="CImage";var Z=i(71028),q=i(17831),W=i(45728),X=i(15170),G=i(97098),_=i(49468),z=i(36617),Q=i(8572),O=i(58756),Y=i(3380),F=i(82699),B=i(85042),K=i(41372),J=i(93961),ee=i(92124),se=i(23793),ie=i(76548),ne=i(17011),ce=i(88859),te=i(38886),re=["512 512","<path fill='var(--ci-primary-color, currentColor)' d='M474.444,19.857a20.336,20.336,0,0,0-21.592-2.781L33.737,213.8v38.066l176.037,70.414L322.69,496h38.074l120.3-455.4A20.342,20.342,0,0,0,474.444,19.857ZM337.257,459.693,240.2,310.37,389.553,146.788l-23.631-21.576L215.4,290.069,70.257,232.012,443.7,56.72Z' class='ci-primary'/>"],le=i(78131),ae=i(97199),de=i(38806),oe=["512 512","<path fill='var(--ci-primary-color, currentColor)' d='M256.25,16A240,240,0,0,0,88,84.977V16H56V144H184V112H106.287A208,208,0,0,1,256.25,48C370.8,48,464,141.2,464,255.75S370.8,463.5,256.25,463.5,48.5,370.3,48.5,255.75h-32A239.75,239.75,0,0,0,425.779,425.279,239.75,239.75,0,0,0,256.25,16Z' class='ci-primary'/><polygon fill='var(--ci-primary-color, currentColor)' points='240 111.951 239.465 288 368 288 368 256 271.563 256 272 112.049 240 111.951' class='ci-primary'/>"],me=i(97572),he=i(2977),xe=i(8134),je=i(30169),ue=["512 512","<path fill='var(--ci-primary-color, currentColor)' d='M400,163.2V68a20.023,20.023,0,0,0-20-20H132a20.023,20.023,0,0,0-20,20v95.2L16,231.766V496H496V231.766Zm53.679,77.667L400,275.96V202.52ZM144,80H368V296.883l-57.166,37.378-46.578-24.152-50.764,24.507L144,292.425ZM263.744,345.89,464,449.727V464H48V450.043ZM48,271.575,179.144,351.2,48,414.509Zm295.446,79.6L464,272.347V413.681ZM112,202.52V273L58.666,240.615Z' class='ci-primary'/>"],ye=["512 512","<path fill='var(--ci-primary-color, currentColor)' d='M449.366,89.648l-.685-.428L362.088,46.559,268.625,171.176l43,57.337a88.529,88.529,0,0,1-83.115,83.114l-57.336-43L46.558,362.088l42.306,85.869.356.725.429.684a25.085,25.085,0,0,0,21.393,11.857h22.344A327.836,327.836,0,0,0,461.222,133.386V111.041A25.084,25.084,0,0,0,449.366,89.648Zm-20.144,43.738c0,163.125-132.712,295.837-295.836,295.837h-18.08L87,371.76l84.18-63.135,46.867,35.149h5.333a120.535,120.535,0,0,0,120.4-120.4v-5.333l-35.149-46.866L371.759,87l57.463,28.311Z' class='ci-primary'/>"],ge=i(63943),pe=i(62293),fe=i(85826),Ne=i(91834),ve=i(22653),be=i(65321),Ae=i(25152),Ce=["512 512","<path fill='var(--ci-primary-color, currentColor)' d='M448.205,392.507c30.519-27.2,47.8-63.455,47.8-101.078,0-39.984-18.718-77.378-52.707-105.3C410.218,158.963,366.432,144,320,144s-90.218,14.963-123.293,42.131C162.718,214.051,144,251.445,144,291.429s18.718,77.378,52.707,105.3c33.075,27.168,76.861,42.13,123.293,42.13,6.187,0,12.412-.273,18.585-.816l10.546,9.141A199.849,199.849,0,0,0,480,496h16V461.943l-4.686-4.685A199.17,199.17,0,0,1,448.205,392.507ZM370.089,423l-21.161-18.341-7.056.865A180.275,180.275,0,0,1,320,406.857c-79.4,0-144-51.781-144-115.428S240.6,176,320,176s144,51.781,144,115.429c0,31.71-15.82,61.314-44.546,83.358l-9.215,7.071,4.252,12.035a231.287,231.287,0,0,0,37.882,67.817A167.839,167.839,0,0,1,370.089,423Z' class='ci-primary'/><path fill='var(--ci-primary-color, currentColor)' d='M60.185,317.476a220.491,220.491,0,0,0,34.808-63.023l4.22-11.975-9.207-7.066C62.918,214.626,48,186.728,48,156.857,48,96.833,109.009,48,184,48c55.168,0,102.767,26.43,124.077,64.3,3.957-.192,7.931-.3,11.923-.3q12.027,0,23.834,1.167c-8.235-21.335-22.537-40.811-42.2-56.961C270.072,30.279,228.3,16,184,16S97.928,30.279,66.364,56.206C33.886,82.885,16,118.63,16,156.857c0,35.8,16.352,70.295,45.25,96.243a188.4,188.4,0,0,1-40.563,60.729L16,318.515V352H32a190.643,190.643,0,0,0,85.231-20.125,157.3,157.3,0,0,1-5.071-33.645A158.729,158.729,0,0,1,60.185,317.476Z' class='ci-primary'/>"],we=["512 512","<rect width='288' height='32' x='112' y='152' fill='var(--ci-primary-color, currentColor)' class='ci-primary'/><rect width='288' height='32' x='112' y='240' fill='var(--ci-primary-color, currentColor)' class='ci-primary'/><rect width='152' height='32' x='112' y='328' fill='var(--ci-primary-color, currentColor)' class='ci-primary'/><path fill='var(--ci-primary-color, currentColor)' d='M480,48H32V464H480ZM448,432H64V80H448Z' class='ci-primary'/>"],De=i(94926),He=i(81949),Me=i(67818),ke=i(28429),Se=i(65572),Ve=i(78114);function Pe(e,s){const i=(0,Ve.a)(e),n=(0,Ve.a)(s),c=Re(i,n),t=Math.abs((0,Se.m)(i,n));i.setDate(i.getDate()-c*t);const r=c*(t-Number(Re(i,n)===-c));return 0===r?0:r}function Re(e,s){const i=e.getFullYear()-s.getFullYear()||e.getMonth()-s.getMonth()||e.getDate()-s.getDate()||e.getHours()-s.getHours()||e.getMinutes()-s.getMinutes()||e.getSeconds()-s.getSeconds()||e.getMilliseconds()-s.getMilliseconds();return i<0?-1:i>0?1:i}var Ee=i(32113);var Te=i(25548);function Ue(e,s){return+(0,Ve.a)(e)-+(0,Ve.a)(s)}function $e(e,s,i){const n=Ue(e,s)/Te.s0;return(c=null===i||void 0===i?void 0:i.roundingMethod,e=>{const s=(c?Math[c]:Math.trunc)(e);return 0===s?0:s})(n);var c}var Ie=i(45753),Le=i(85923),Ze=i(79894),qe=i(44414);const We=()=>{const{t:e}=(0,Me.Bd)(),{id:s}=(0,ke.g)(),i=(0,ke.Zp)(),[T,U]=(0,n.useState)(!1),[$,I]=(0,n.useState)(null),[Se,Ve]=(0,n.useState)("overview"),[Re,Te]=(0,n.useState)(!1),[Ue,We]=(0,n.useState)(!1),[Xe,Ge]=(0,n.useState)(!1),[_e,ze]=(0,n.useState)(!1),[Qe,Oe]=(0,n.useState)(!1),[Ye,Fe]=(0,n.useState)(""),[Be,Ke]=(0,n.useState)("parent"),[Je,es]=(0,n.useState)(""),ss=async()=>{U(!0);try{const n=await Ze.X.get("/api/incident/".concat(s));n.success?I(n.data):(Le.oR.error(e("incident.errors.loadingDetails")),i("/incidents"))}catch(n){console.error("Failed to load incident:",n),Le.oR.error(e("incident.errors.loadingDetails")),i("/incidents")}finally{U(!1)}};(0,n.useEffect)((()=>{s&&ss()}),[s]);const is=async()=>{Oe(!0),await ss(),Oe(!1),Le.oR.success(e("common.refreshed"))};if(T||!$)return(0,qe.jsx)(c.T,{fluid:!0,className:"d-flex justify-content-center align-items-center",style:{minHeight:"400px"},children:(0,qe.jsx)(t.J,{color:"primary"})});const ns=Pe(new Date,(0,Ee.H)($.incidentDateTime)),cs=$.reportedDateTime?$e((0,Ee.H)($.reportedDateTime),(0,Ee.H)($.incidentDateTime)):0,ts="Completed"===$.investigationStatus?100:"InProgress"===$.investigationStatus?50:0,rs=e=>{switch(e){case 5:return"danger";case 4:return"warning";case 3:return"info";case 2:return"primary";default:return"secondary"}},ls=e=>{switch(e){case"Closed":return"success";case"Resolved":return"info";case"InProgress":return"primary";case"Acknowledged":return"warning";default:return"secondary"}};return(0,qe.jsxs)(c.T,{fluid:!0,children:[(0,qe.jsx)(h.s,{className:"mb-4",children:(0,qe.jsx)(x.U,{children:(0,qe.jsxs)("div",{className:"d-flex justify-content-between align-items-start",children:[(0,qe.jsxs)("div",{children:[(0,qe.jsxs)("h2",{children:[e("incident.details.title")," #",$.incidentNumber,$.isAnonymous&&(0,qe.jsx)(v.$,{color:"secondary",className:"ms-2",children:"Anonymous"})]}),(0,qe.jsx)("h5",{className:"text-muted mb-0",children:$.title})]}),(0,qe.jsxs)("div",{className:"d-flex gap-2",children:[(0,qe.jsx)(r.Q,{color:"secondary",variant:"outline",size:"sm",onClick:is,disabled:Qe,children:Qe?(0,qe.jsx)(t.J,{size:"sm"}):(0,qe.jsx)(He.Ay,{icon:ne.Q})}),(0,qe.jsxs)(l.j,{children:[(0,qe.jsxs)(a.V,{color:"primary",size:"sm",children:[(0,qe.jsx)(He.Ay,{icon:ce.O,className:"me-2"}),e("incident.actions.title")]}),(0,qe.jsxs)(d.Q,{children:["NotStarted"===$.investigationStatus&&(0,qe.jsxs)(o.k,{onClick:()=>Te(!0),children:[(0,qe.jsx)(He.Ay,{icon:te.S,className:"me-2"}),e("incident.actions.startInvestigation")]}),$.isStudentIncident&&!$.parentNotified&&(0,qe.jsxs)(o.k,{onClick:()=>We(!0),children:[(0,qe.jsx)(He.Ay,{icon:re,className:"me-2"}),e("incident.actions.notifyParent")]}),(0,qe.jsxs)(o.k,{onClick:()=>i("/incidents/".concat(s,"/edit")),children:[(0,qe.jsx)(He.Ay,{icon:le.K,className:"me-2"}),e("incident.actions.edit")]}),(0,qe.jsx)(m.A,{}),(0,qe.jsxs)(o.k,{onClick:()=>window.print(),children:[(0,qe.jsx)(He.Ay,{icon:ae.C,className:"me-2"}),e("incident.actions.print")]}),(0,qe.jsxs)(o.k,{children:[(0,qe.jsx)(He.Ay,{icon:de.s,className:"me-2"}),e("incident.actions.export")]})]})]}),(0,qe.jsxs)(r.Q,{color:"info",variant:"ghost",size:"sm",onClick:()=>ze(!0),children:[(0,qe.jsx)(He.Ay,{icon:oe,className:"me-2"}),e("incident.timeline.title")]})]})]})})}),(0,qe.jsxs)(y.E,{className:"mb-4",children:[(0,qe.jsx)(g.V,{children:(0,qe.jsxs)(Z.b,{variant:"tabs",role:"tablist",children:[(0,qe.jsx)(q.g,{children:(0,qe.jsxs)(W.H,{active:"overview"===Se,onClick:()=>Ve("overview"),children:[(0,qe.jsx)(He.Ay,{icon:De.v,className:"me-2"}),e("incident.tabs.overview")]})}),(0,qe.jsx)(q.g,{children:(0,qe.jsxs)(W.H,{active:"people"===Se,onClick:()=>Ve("people"),children:[(0,qe.jsx)(He.Ay,{icon:je.g,className:"me-2"}),e("incident.tabs.people")]})}),(0,qe.jsx)(q.g,{children:(0,qe.jsxs)(W.H,{active:"medical"===Se,onClick:()=>Ve("medical"),children:[(0,qe.jsx)(He.Ay,{icon:fe.k,className:"me-2"}),e("incident.tabs.medical")]})}),(0,qe.jsx)(q.g,{children:(0,qe.jsxs)(W.H,{active:"investigation"===Se,onClick:()=>Ve("investigation"),children:[(0,qe.jsx)(He.Ay,{icon:te.S,className:"me-2"}),e("incident.tabs.investigation")]})}),(0,qe.jsx)(q.g,{children:(0,qe.jsxs)(W.H,{active:"compliance"===Se,onClick:()=>Ve("compliance"),children:[(0,qe.jsx)(He.Ay,{icon:ve.l,className:"me-2"}),e("incident.tabs.compliance")]})}),(0,qe.jsx)(q.g,{children:(0,qe.jsxs)(W.H,{active:"evidence"===Se,onClick:()=>Ve("evidence"),children:[(0,qe.jsx)(He.Ay,{icon:be.u,className:"me-2"}),e("incident.tabs.evidence")]})})]})}),(0,qe.jsx)(p.W,{children:(0,qe.jsxs)(X.e,{children:[(0,qe.jsx)(G.x,{visible:"overview"===Se,children:(0,qe.jsxs)("div",{children:[(0,qe.jsxs)(h.s,{className:"mb-4",children:[(0,qe.jsx)(x.U,{xs:12,sm:6,lg:3,children:(0,qe.jsx)(j.N,{className:"mb-3",color:rs($.severity),icon:(0,qe.jsx)(He.Ay,{icon:me.p,height:24}),title:e("incident.fields.severity"),value:$.severityDisplay})}),(0,qe.jsx)(x.U,{xs:12,sm:6,lg:3,children:(0,qe.jsx)(j.N,{className:"mb-3",color:ls($.status),icon:(0,qe.jsx)(He.Ay,{icon:he.j,height:24}),title:e("incident.fields.status"),value:$.statusDisplay})}),(0,qe.jsx)(x.U,{xs:12,sm:6,lg:3,children:(0,qe.jsx)(j.N,{className:"mb-3",color:"info",icon:(0,qe.jsx)(He.Ay,{icon:xe.V,height:24}),title:e("incident.metrics.age"),value:"".concat(ns," ").concat(e("common.days"))})}),(0,qe.jsx)(x.U,{xs:12,sm:6,lg:3,children:(0,qe.jsx)(j.N,{className:"mb-3",color:"warning",icon:(0,qe.jsx)(He.Ay,{icon:te.S,height:24}),title:e("incident.investigation.progress"),value:"".concat(ts,"%")})})]}),$.isRegulatoryOverdue&&(0,qe.jsxs)(u.k,{color:"danger",className:"mb-4",children:[(0,qe.jsx)(He.Ay,{icon:me.p,className:"me-2"}),(0,qe.jsx)("strong",{children:e("incident.alerts.regulatoryOverdue")}),$.regulatoryDeadline&&(0,qe.jsxs)("span",{className:"ms-2",children:["- ",e("incident.alerts.deadline"),": ",(0,Ie.GP)((0,Ee.H)($.regulatoryDeadline),"dd/MM/yyyy")]})]}),$.isStudentIncident&&!$.parentNotified&&(0,qe.jsxs)(u.k,{color:"warning",className:"mb-4",children:[(0,qe.jsx)(He.Ay,{icon:je.g,className:"me-2"}),(0,qe.jsx)("strong",{children:e("incident.alerts.parentNotRequired")}),(0,qe.jsx)(r.Q,{color:"warning",variant:"outline",size:"sm",className:"ms-3",onClick:()=>We(!0),children:e("incident.actions.notifyNow")})]}),(0,qe.jsxs)(y.E,{className:"mb-4",children:[(0,qe.jsx)(g.V,{children:(0,qe.jsx)("h5",{className:"mb-0",children:e("incident.sections.basicInfo")})}),(0,qe.jsxs)(p.W,{children:[(0,qe.jsxs)(h.s,{children:[(0,qe.jsx)(x.U,{md:6,children:(0,qe.jsxs)(f.X,{flush:!0,children:[(0,qe.jsx)(N.y,{className:"d-flex justify-content-between align-items-start",children:(0,qe.jsxs)("div",{className:"ms-2 me-auto",children:[(0,qe.jsx)("div",{className:"fw-bold",children:e("incident.fields.number")}),$.incidentNumber,$.isAnonymous&&(0,qe.jsx)(v.$,{color:"secondary",className:"ms-2",children:"Anonymous"})]})}),(0,qe.jsx)(N.y,{className:"d-flex justify-content-between align-items-start",children:(0,qe.jsxs)("div",{className:"ms-2 me-auto",children:[(0,qe.jsx)("div",{className:"fw-bold",children:e("incident.fields.dateTime")}),(0,Ie.GP)((0,Ee.H)($.incidentDateTime),"dd/MM/yyyy HH:mm"),(0,qe.jsxs)("small",{className:"text-muted ms-2",children:["(",ns," ",e("incident.metrics.daysAgo"),")"]})]})}),(0,qe.jsx)(N.y,{className:"d-flex justify-content-between align-items-start",children:(0,qe.jsxs)("div",{className:"ms-2 me-auto",children:[(0,qe.jsx)("div",{className:"fw-bold",children:e("incident.fields.category")}),$.categoryDisplay]})}),(0,qe.jsx)(N.y,{className:"d-flex justify-content-between align-items-start",children:(0,qe.jsxs)("div",{className:"ms-2 me-auto",children:[(0,qe.jsx)("div",{className:"fw-bold",children:e("incident.fields.type")}),$.typeDisplay]})})]})}),(0,qe.jsx)(x.U,{md:6,children:(0,qe.jsxs)(f.X,{flush:!0,children:[(0,qe.jsx)(N.y,{className:"d-flex justify-content-between align-items-start",children:(0,qe.jsxs)("div",{className:"ms-2 me-auto",children:[(0,qe.jsx)("div",{className:"fw-bold",children:e("incident.fields.reportedBy")}),$.reportedBy," (",$.reporterTypeDisplay,")",$.reporterEmail&&(0,qe.jsxs)("div",{className:"small text-muted",children:[(0,qe.jsx)(He.Ay,{icon:ue,size:"sm",className:"me-1"}),$.reporterEmail]}),$.reporterPhone&&(0,qe.jsxs)("div",{className:"small text-muted",children:[(0,qe.jsx)(He.Ay,{icon:ye,size:"sm",className:"me-1"}),$.reporterPhone]})]})}),(0,qe.jsx)(N.y,{className:"d-flex justify-content-between align-items-start",children:(0,qe.jsxs)("div",{className:"ms-2 me-auto",children:[(0,qe.jsx)("div",{className:"fw-bold",children:e("incident.fields.reportedAt")}),(0,Ie.GP)((0,Ee.H)($.reportedDateTime),"dd/MM/yyyy HH:mm"),(0,qe.jsxs)("small",{className:"text-muted ms-2",children:["(",cs," ",e("incident.metrics.hoursToReport"),")"]})]})}),(0,qe.jsx)(N.y,{className:"d-flex justify-content-between align-items-start",children:(0,qe.jsxs)("div",{className:"ms-2 me-auto",children:[(0,qe.jsx)("div",{className:"fw-bold",children:e("incident.fields.urgency")}),(0,qe.jsx)(v.$,{color:$.urgency>=4?"danger":$.urgency>=3?"warning":"info",children:$.urgencyDisplay})]})})]})})]}),(0,qe.jsx)("hr",{}),(0,qe.jsxs)("div",{children:[(0,qe.jsx)("h6",{className:"mb-3",children:e("incident.fields.description")}),(0,qe.jsx)("p",{className:"text-break",children:$.description})]})]})]}),(0,qe.jsxs)(y.E,{className:"mb-4",children:[(0,qe.jsx)(g.V,{children:(0,qe.jsxs)("h5",{className:"mb-0",children:[(0,qe.jsx)(He.Ay,{icon:ge.r,className:"me-2"}),e("incident.sections.location")]})}),(0,qe.jsx)(p.W,{children:(0,qe.jsxs)(h.s,{children:[(0,qe.jsx)(x.U,{md:6,children:(0,qe.jsxs)(f.X,{flush:!0,children:[(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.building"),":"]})," ",$.building]}),(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.floor"),":"]})," ",$.floor]}),$.room&&(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.room"),":"]})," ",$.room]}),(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.location"),":"]})," ",$.location]})]})}),(0,qe.jsxs)(x.U,{md:6,children:[$.specificLocation&&(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.specificLocation"),":"]}),(0,qe.jsx)("p",{className:"mb-0 mt-1",children:$.specificLocation})]}),$.latitude&&$.longitude&&(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.gpsCoordinates"),":"]}),(0,qe.jsxs)("div",{className:"mt-1",children:[(0,qe.jsxs)(v.$,{color:"info",children:[$.latitude.toFixed(6),", ",$.longitude.toFixed(6)]}),(0,qe.jsxs)(r.Q,{color:"primary",variant:"ghost",size:"sm",className:"ms-2",onClick:()=>window.open("https://maps.google.com/?q=".concat($.latitude,",").concat($.longitude),"_blank"),children:[(0,qe.jsx)(He.Ay,{icon:ge.r,className:"me-1"}),e("incident.actions.viewOnMap")]})]})]})]})]})})]})]})}),(0,qe.jsx)(G.x,{visible:"people"===Se,children:(0,qe.jsxs)("div",{children:[$.personAffectedName&&(0,qe.jsxs)(y.E,{className:"mb-4",children:[(0,qe.jsx)(g.V,{children:(0,qe.jsxs)("h5",{className:"mb-0",children:[(0,qe.jsx)(He.Ay,{icon:pe.o,className:"me-2"}),e("incident.sections.personAffected")]})}),(0,qe.jsx)(p.W,{children:(0,qe.jsxs)(h.s,{children:[(0,qe.jsx)(x.U,{md:6,children:(0,qe.jsxs)(f.X,{flush:!0,children:[(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.name"),":"]})," ",$.personAffectedName]}),(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.type"),":"]})," ",$.personAffectedTypeDisplay]}),$.personAffectedId&&(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.id"),":"]})," ",$.personAffectedId]}),$.personAffectedDepartment&&(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.department"),":"]})," ",$.personAffectedDepartment]})]})}),(0,qe.jsxs)(x.U,{md:6,children:[$.personAffectedClass&&(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.class"),":"]})," ",$.personAffectedClass]}),$.personAffectedAge&&(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.age"),":"]})," ",$.personAffectedAge," years"]}),$.personAffectedContact&&(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.contact"),":"]})," ",$.personAffectedContact]})]})]})})]}),$.parentGuardianName&&(0,qe.jsxs)(y.E,{className:"mb-4",children:[(0,qe.jsx)(g.V,{children:(0,qe.jsxs)("h5",{className:"mb-0",children:[(0,qe.jsx)(He.Ay,{icon:je.g,className:"me-2"}),e("incident.sections.parentInfo")]})}),(0,qe.jsx)(p.W,{children:(0,qe.jsxs)(h.s,{children:[(0,qe.jsx)(x.U,{md:6,children:(0,qe.jsxs)(f.X,{flush:!0,children:[(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.parentName"),":"]})," ",$.parentGuardianName]}),(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.parentContact"),":"]})," ",$.parentGuardianContact]}),$.parentGuardianEmail&&(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.parentEmail"),":"]})," ",$.parentGuardianEmail]})]})}),(0,qe.jsx)(x.U,{md:6,children:(0,qe.jsxs)(f.X,{flush:!0,children:[(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.notificationStatus"),":"]}),$.parentNotified?(0,qe.jsxs)(v.$,{color:"success",className:"ms-2",children:[(0,qe.jsx)(He.Ay,{icon:he.j,size:"sm",className:"me-1"}),e("incident.parent.notified")]}):(0,qe.jsxs)(v.$,{color:"warning",className:"ms-2",children:[(0,qe.jsx)(He.Ay,{icon:xe.V,size:"sm",className:"me-1"}),e("incident.parent.pending")]})]}),$.parentNotificationTime&&(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.notifiedAt"),":"]})," ",(0,Ie.GP)((0,Ee.H)($.parentNotificationTime),"dd/MM/yyyy HH:mm")]}),$.parentAcknowledgment&&(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.acknowledged"),":"]})," ",(0,qe.jsx)(v.$,{color:"success",children:(0,Ie.GP)((0,Ee.H)($.parentAcknowledgmentTime),"dd/MM/yyyy HH:mm")})]})]})})]})})]}),$.witnessNames&&$.witnessNames.length>0&&(0,qe.jsxs)(y.E,{className:"mb-4",children:[(0,qe.jsx)(g.V,{children:(0,qe.jsxs)("h5",{className:"mb-0",children:[(0,qe.jsx)(He.Ay,{icon:je.g,className:"me-2"}),e("incident.sections.witnesses")]})}),(0,qe.jsx)(p.W,{children:(0,qe.jsxs)(b._,{hover:!0,children:[(0,qe.jsx)(A.w,{children:(0,qe.jsxs)(C.Y,{children:[(0,qe.jsx)(w.$,{children:"#"}),(0,qe.jsx)(w.$,{children:e("incident.fields.name")}),(0,qe.jsx)(w.$,{children:e("incident.fields.contact")}),(0,qe.jsx)(w.$,{children:e("incident.fields.statement")})]})}),(0,qe.jsx)(D.C,{children:$.witnessNames.map(((s,i)=>(0,qe.jsxs)(C.Y,{children:[(0,qe.jsx)(H.c,{children:i+1}),(0,qe.jsx)(H.c,{children:s}),(0,qe.jsx)(H.c,{children:$.witnessContacts[i]||"-"}),(0,qe.jsx)(H.c,{children:$.witnessStatements[i]?(0,qe.jsx)(v.$,{color:"success",children:e("incident.witness.provided")}):(0,qe.jsx)(v.$,{color:"secondary",children:e("incident.witness.pending")})})]},i)))})]})})]})]})}),(0,qe.jsx)(G.x,{visible:"medical"===Se,children:(0,qe.jsxs)("div",{children:[(0,qe.jsxs)(y.E,{className:"mb-4",children:[(0,qe.jsx)(g.V,{children:(0,qe.jsxs)("h5",{className:"mb-0",children:[(0,qe.jsx)(He.Ay,{icon:fe.k,className:"me-2"}),e("incident.sections.medicalResponse")]})}),(0,qe.jsx)(p.W,{children:(0,qe.jsxs)(h.s,{children:[(0,qe.jsx)(x.U,{md:6,children:(0,qe.jsxs)(f.X,{flush:!0,children:[(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.requiresMedical"),":"]}),$.requiresMedicalAttention?(0,qe.jsx)(v.$,{color:"danger",className:"ms-2",children:"Yes"}):(0,qe.jsx)(v.$,{color:"success",className:"ms-2",children:"No"})]}),$.firstAidProvided&&(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.firstAid"),":"]}),(0,qe.jsx)("p",{className:"mb-0 mt-1",children:$.firstAidProvided})]}),(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.emergencyServices"),":"]}),$.emergencyServicesNotified?(0,qe.jsxs)("span",{children:[(0,qe.jsx)(v.$,{color:"warning",className:"ms-2",children:"Notified"}),$.emergencyServiceType&&(0,qe.jsxs)("span",{className:"ms-2",children:["- ",$.emergencyServiceType]})]}):(0,qe.jsx)(v.$,{color:"secondary",className:"ms-2",children:"Not Required"})]})]})}),(0,qe.jsxs)(x.U,{md:6,children:[$.hospitalName&&(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.hospital"),":"]})," ",$.hospitalName]}),$.medicalTreatmentDetails&&(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.treatment"),":"]}),(0,qe.jsx)("p",{className:"mb-0 mt-1",children:$.medicalTreatmentDetails})]}),(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.followUpRequired"),":"]}),$.medicalFollowUpRequired?(0,qe.jsx)(v.$,{color:"warning",className:"ms-2",children:"Yes"}):(0,qe.jsx)(v.$,{color:"success",className:"ms-2",children:"No"})]})]})]})})]}),$.immediateActions&&(0,qe.jsxs)(y.E,{className:"mb-4",children:[(0,qe.jsx)(g.V,{children:(0,qe.jsx)("h5",{className:"mb-0",children:e("incident.sections.immediateActions")})}),(0,qe.jsxs)(p.W,{children:[(0,qe.jsx)("p",{className:"mb-3",children:$.immediateActions}),(0,qe.jsxs)(h.s,{children:[(0,qe.jsx)(x.U,{md:6,children:(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.evacuationRequired"),":"]}),$.evacuationRequired?(0,qe.jsx)(v.$,{color:"danger",className:"ms-2",children:"Yes"}):(0,qe.jsx)(v.$,{color:"success",className:"ms-2",children:"No"})]})}),(0,qe.jsx)(x.U,{md:6,children:(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.areaSecured"),":"]}),$.areaSecured?(0,qe.jsx)(v.$,{color:"success",className:"ms-2",children:"Yes"}):(0,qe.jsx)(v.$,{color:"warning",className:"ms-2",children:"No"})]})})]})]})]}),$.activityType&&(0,qe.jsxs)(y.E,{children:[(0,qe.jsx)(g.V,{children:(0,qe.jsxs)("h5",{className:"mb-0",children:[(0,qe.jsx)(He.Ay,{icon:Ne.$,className:"me-2"}),e("incident.sections.schoolContext")]})}),(0,qe.jsx)(p.W,{children:(0,qe.jsxs)(h.s,{children:[(0,qe.jsx)(x.U,{md:6,children:(0,qe.jsxs)(f.X,{flush:!0,children:[(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.activityType"),":"]})," ",$.activityType]}),$.subjectClass&&(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.subject"),":"]})," ",$.subjectClass]}),$.teacherInCharge&&(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.teacher"),":"]})," ",$.teacherInCharge]})]})}),(0,qe.jsxs)(x.U,{md:6,children:[$.supervisorPresent&&(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.supervisor"),":"]})," ",$.supervisorPresent]}),void 0!==$.studentsPresent&&(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.studentsPresent"),":"]})," ",$.studentsPresent]}),(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.conditions"),":"]}),$.weatherConditions&&(0,qe.jsx)(v.$,{color:"info",className:"ms-2",children:$.weatherConditions}),$.lightingConditions&&(0,qe.jsx)(v.$,{color:"info",className:"ms-2",children:$.lightingConditions})]})]})]})})]})]})}),(0,qe.jsx)(G.x,{visible:"investigation"===Se,children:(0,qe.jsxs)("div",{children:[(0,qe.jsxs)(y.E,{className:"mb-4",children:[(0,qe.jsx)(g.V,{children:(0,qe.jsxs)("h5",{className:"mb-0",children:[(0,qe.jsx)(He.Ay,{icon:te.S,className:"me-2"}),e("incident.sections.investigationStatus")]})}),(0,qe.jsxs)(p.W,{children:[(0,qe.jsx)(h.s,{className:"mb-3",children:(0,qe.jsx)(x.U,{children:(0,qe.jsx)(M.f,{height:20,children:(0,qe.jsxs)(k.E,{value:ts,color:100===ts?"success":ts>0?"primary":"secondary",children:[$.investigationStatusDisplay," (",ts,"%)"]})})})}),(0,qe.jsxs)(h.s,{children:[(0,qe.jsx)(x.U,{md:6,children:(0,qe.jsxs)(f.X,{flush:!0,children:[(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.status"),":"]}),(0,qe.jsx)(v.$,{color:"Completed"===$.investigationStatus?"success":"InProgress"===$.investigationStatus?"primary":"secondary",className:"ms-2",children:$.investigationStatusDisplay})]}),$.investigatedBy&&(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.investigator"),":"]})," ",$.investigatedBy]}),$.investigationStartDate&&(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.startDate"),":"]})," ",(0,Ie.GP)((0,Ee.H)($.investigationStartDate),"dd/MM/yyyy")]})]})}),(0,qe.jsxs)(x.U,{md:6,children:[$.investigationDueDate&&(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.dueDate"),":"]})," ",(0,Ie.GP)((0,Ee.H)($.investigationDueDate),"dd/MM/yyyy"),new Date($.investigationDueDate)<new Date&&"Completed"!==$.investigationStatus&&(0,qe.jsx)(v.$,{color:"danger",className:"ms-2",children:"Overdue"})]}),$.investigationCompletedDate&&(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.completedDate"),":"]})," ",(0,Ie.GP)((0,Ee.H)($.investigationCompletedDate),"dd/MM/yyyy")]}),(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.requiresCAPAActions"),":"]}),$.requiresCAPAActions?(0,qe.jsx)(v.$,{color:"warning",className:"ms-2",children:"Yes"}):(0,qe.jsx)(v.$,{color:"success",className:"ms-2",children:"No"})]})]})]})]})]}),$.rootCauseAnalysis&&(0,qe.jsxs)(y.E,{className:"mb-4",children:[(0,qe.jsx)(g.V,{children:(0,qe.jsx)("h5",{className:"mb-0",children:e("incident.sections.rootCauseAnalysis")})}),(0,qe.jsx)(p.W,{children:(0,qe.jsxs)(S.E,{children:[(0,qe.jsxs)(V.l,{children:[(0,qe.jsx)(P.B,{children:e("incident.fields.rootCause")}),(0,qe.jsx)(R.i,{children:$.rootCauseAnalysis})]}),$.contributingFactors&&(0,qe.jsxs)(V.l,{children:[(0,qe.jsx)(P.B,{children:e("incident.fields.contributingFactors")}),(0,qe.jsx)(R.i,{children:$.contributingFactors})]}),$.lessonsLearned&&(0,qe.jsxs)(V.l,{children:[(0,qe.jsx)(P.B,{children:e("incident.fields.lessonsLearned")}),(0,qe.jsx)(R.i,{children:$.lessonsLearned})]})]})})]}),$.capaActions&&$.capaActions.length>0&&(0,qe.jsxs)(y.E,{children:[(0,qe.jsx)(g.V,{children:(0,qe.jsxs)("h5",{className:"mb-0",children:[(0,qe.jsx)(He.Ay,{icon:ce.O,className:"me-2"}),e("incident.sections.capaActions")]})}),(0,qe.jsx)(p.W,{children:(0,qe.jsxs)(b._,{hover:!0,children:[(0,qe.jsx)(A.w,{children:(0,qe.jsxs)(C.Y,{children:[(0,qe.jsx)(w.$,{children:e("incident.capa.number")}),(0,qe.jsx)(w.$,{children:e("incident.capa.description")}),(0,qe.jsx)(w.$,{children:e("incident.capa.assignedTo")}),(0,qe.jsx)(w.$,{children:e("incident.capa.dueDate")}),(0,qe.jsx)(w.$,{children:e("incident.capa.status")})]})}),(0,qe.jsx)(D.C,{children:$.capaActions.map((e=>(0,qe.jsxs)(C.Y,{children:[(0,qe.jsx)(H.c,{children:e.actionNumber}),(0,qe.jsx)(H.c,{children:e.description}),(0,qe.jsx)(H.c,{children:e.assignedTo}),(0,qe.jsx)(H.c,{children:(0,Ie.GP)((0,Ee.H)(e.dueDate),"dd/MM/yyyy")}),(0,qe.jsx)(H.c,{children:(0,qe.jsx)(v.$,{color:"Completed"===e.status?"success":"InProgress"===e.status?"primary":"secondary",children:e.status})})]},e.id)))})]})})]})]})}),(0,qe.jsx)(G.x,{visible:"compliance"===Se,children:(0,qe.jsxs)("div",{children:[(0,qe.jsxs)(y.E,{className:"mb-4",children:[(0,qe.jsx)(g.V,{children:(0,qe.jsxs)("h5",{className:"mb-0",children:[(0,qe.jsx)(He.Ay,{icon:ve.l,className:"me-2"}),e("incident.sections.regulatoryCompliance")]})}),(0,qe.jsxs)(p.W,{children:[(0,qe.jsxs)(h.s,{children:[(0,qe.jsxs)(x.U,{md:6,children:[(0,qe.jsx)("h6",{children:e("incident.compliance.bpjs")}),(0,qe.jsxs)(f.X,{flush:!0,children:[(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.required"),":"]}),$.requiresBPJSReporting?(0,qe.jsx)(v.$,{color:"warning",className:"ms-2",children:"Yes"}):(0,qe.jsx)(v.$,{color:"secondary",className:"ms-2",children:"No"})]}),$.bpjsReferenceNumber&&(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.referenceNumber"),":"]})," ",$.bpjsReferenceNumber]}),$.bpjsReportedDate&&(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.reportedDate"),":"]})," ",(0,Ie.GP)((0,Ee.H)($.bpjsReportedDate),"dd/MM/yyyy")]})]})]}),(0,qe.jsxs)(x.U,{md:6,children:[(0,qe.jsx)("h6",{children:e("incident.compliance.ministry")}),(0,qe.jsxs)(f.X,{flush:!0,children:[(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.required"),":"]}),$.requiresMinistryReporting?(0,qe.jsx)(v.$,{color:"warning",className:"ms-2",children:"Yes"}):(0,qe.jsx)(v.$,{color:"secondary",className:"ms-2",children:"No"})]}),$.ministryReferenceNumber&&(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.referenceNumber"),":"]})," ",$.ministryReferenceNumber]}),$.ministryReportedDate&&(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.reportedDate"),":"]})," ",(0,Ie.GP)((0,Ee.H)($.ministryReportedDate),"dd/MM/yyyy")]})]})]})]}),$.regulatoryDeadline&&(0,qe.jsxs)(E.M,{color:$.regulatoryDeadlineMet?"success":new Date($.regulatoryDeadline)>new Date?"warning":"danger",className:"mt-3",children:[(0,qe.jsxs)("strong",{children:[e("incident.compliance.deadline"),":"]})," ",(0,Ie.GP)((0,Ee.H)($.regulatoryDeadline),"dd/MM/yyyy HH:mm"),!$.regulatoryDeadlineMet&&new Date($.regulatoryDeadline)<new Date&&(0,qe.jsxs)("span",{className:"ms-2",children:["- ",(0,qe.jsx)("strong",{children:e("incident.compliance.overdue")})]})]})]})]}),($.insuranceClaimNumber||$.legalCaseNumber)&&(0,qe.jsxs)(y.E,{children:[(0,qe.jsx)(g.V,{children:(0,qe.jsx)("h5",{className:"mb-0",children:e("incident.sections.insuranceLegal")})}),(0,qe.jsx)(p.W,{children:(0,qe.jsxs)(h.s,{children:[$.insuranceClaimNumber&&(0,qe.jsxs)(x.U,{md:6,children:[(0,qe.jsx)("h6",{children:e("incident.insurance.title")}),(0,qe.jsxs)(f.X,{flush:!0,children:[(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.claimNumber"),":"]})," ",$.insuranceClaimNumber]}),$.estimatedCost&&(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.estimatedCost"),":"]})," $",$.estimatedCost.toLocaleString()]}),$.actualCost&&(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.actualCost"),":"]})," $",$.actualCost.toLocaleString()]})]})]}),$.legalCaseNumber&&(0,qe.jsxs)(x.U,{md:6,children:[(0,qe.jsx)("h6",{children:e("incident.legal.title")}),(0,qe.jsxs)(f.X,{flush:!0,children:[(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.caseNumber"),":"]})," ",$.legalCaseNumber]}),$.legalStatus&&(0,qe.jsxs)(N.y,{children:[(0,qe.jsxs)("strong",{children:[e("incident.fields.status"),":"]})," ",$.legalStatus]})]})]})]})})]})]})}),(0,qe.jsx)(G.x,{visible:"evidence"===Se,children:(0,qe.jsxs)("div",{children:[$.photoUrls&&$.photoUrls.length>0&&(0,qe.jsxs)(y.E,{className:"mb-4",children:[(0,qe.jsx)(g.V,{children:(0,qe.jsxs)("h5",{className:"mb-0",children:[(0,qe.jsx)(He.Ay,{icon:be.u,className:"me-2"}),e("incident.evidence.photos")," (",$.photoUrls.length,")"]})}),(0,qe.jsx)(p.W,{children:(0,qe.jsx)(h.s,{children:$.photoUrls.map(((e,s)=>(0,qe.jsx)(x.U,{xs:6,md:4,lg:3,className:"mb-3",children:(0,qe.jsx)(L,{src:e,alt:"Evidence photo ".concat(s+1),thumbnail:!0,style:{cursor:"pointer"},onClick:()=>window.open(e,"_blank")})},s)))})})]}),$.videoUrls&&$.videoUrls.length>0&&(0,qe.jsxs)(y.E,{className:"mb-4",children:[(0,qe.jsx)(g.V,{children:(0,qe.jsxs)("h5",{className:"mb-0",children:[(0,qe.jsx)(He.Ay,{icon:be.u,className:"me-2"}),e("incident.evidence.videos")," (",$.videoUrls.length,")"]})}),(0,qe.jsx)(p.W,{children:(0,qe.jsx)(f.X,{children:$.videoUrls.map(((s,i)=>(0,qe.jsxs)(N.y,{className:"d-flex justify-content-between align-items-center",children:[(0,qe.jsxs)("span",{children:["Video ",i+1]}),(0,qe.jsx)(r.Q,{color:"primary",size:"sm",onClick:()=>window.open(s,"_blank"),children:e("common.view")})]},i)))})})]}),$.documentUrls&&$.documentUrls.length>0&&(0,qe.jsxs)(y.E,{children:[(0,qe.jsx)(g.V,{children:(0,qe.jsxs)("h5",{className:"mb-0",children:[(0,qe.jsx)(He.Ay,{icon:Ae.T,className:"me-2"}),e("incident.evidence.documents")," (",$.documentUrls.length,")"]})}),(0,qe.jsx)(p.W,{children:(0,qe.jsx)(f.X,{children:$.documentUrls.map(((s,i)=>(0,qe.jsxs)(N.y,{className:"d-flex justify-content-between align-items-center",children:[(0,qe.jsxs)("span",{children:["Document ",i+1]}),(0,qe.jsxs)(r.Q,{color:"primary",size:"sm",onClick:()=>window.open(s,"_blank"),children:[(0,qe.jsx)(He.Ay,{icon:de.s,className:"me-2"}),e("common.download")]})]},i)))})})]})]})})]})})]}),(()=>{var s;return(0,qe.jsxs)(y.E,{children:[(0,qe.jsxs)(g.V,{className:"d-flex justify-content-between align-items-center",children:[(0,qe.jsxs)("h5",{className:"mb-0",children:[(0,qe.jsx)(He.Ay,{icon:Ce,className:"me-2"}),e("incident.sections.comments")," (",(null===(s=$.comments)||void 0===s?void 0:s.length)||0,")"]}),(0,qe.jsxs)(r.Q,{color:"primary",size:"sm",onClick:()=>Ge(!0),children:[(0,qe.jsx)(He.Ay,{icon:we,className:"me-2"}),e("incident.comments.add")]})]}),(0,qe.jsx)(p.W,{children:$.comments&&$.comments.length>0?(0,qe.jsx)("div",{className:"timeline",children:$.comments.map((e=>(0,qe.jsxs)("div",{className:"timeline-item",children:[(0,qe.jsx)("div",{className:"timeline-marker"}),(0,qe.jsxs)("div",{className:"timeline-content",children:[(0,qe.jsxs)("div",{className:"d-flex justify-content-between align-items-start mb-2",children:[(0,qe.jsxs)("div",{children:[(0,qe.jsx)("strong",{children:e.author}),e.isInternal&&(0,qe.jsx)(v.$,{color:"info",className:"ms-2",children:"Internal"})]}),(0,qe.jsx)("small",{className:"text-muted",children:(0,Ie.GP)((0,Ee.H)(e.timestamp),"dd/MM/yyyy HH:mm")})]}),(0,qe.jsx)("p",{className:"mb-0",children:e.content})]})]},e.id)))}):(0,qe.jsx)("p",{className:"text-muted mb-0",children:e("incident.comments.none")})})]})})(),(0,qe.jsxs)(_.z,{visible:Re,onClose:()=>Te(!1),children:[(0,qe.jsx)(z.E,{children:(0,qe.jsx)(Q.l,{children:e("incident.investigation.start")})}),(0,qe.jsx)(O.T,{children:(0,qe.jsx)(Y.I,{rows:4,placeholder:e("incident.investigation.notesPlaceholder"),value:Je,onChange:e=>es(e.target.value)})}),(0,qe.jsxs)(F.I,{children:[(0,qe.jsx)(r.Q,{color:"secondary",onClick:()=>Te(!1),children:e("common.cancel")}),(0,qe.jsx)(r.Q,{color:"primary",onClick:async()=>{try{const i=await Ze.X.post("/api/incident/".concat(s,"/investigate"),{investigator:"Current User",notes:Je});i.success?(Le.oR.success(e("incident.investigation.started")),Te(!1),ss()):Le.oR.error(i.message||e("incident.investigation.startError"))}catch(i){Le.oR.error(e("incident.investigation.startError"))}},children:e("common.start")})]})]}),(0,qe.jsxs)(_.z,{visible:Ue,onClose:()=>We(!1),children:[(0,qe.jsx)(z.E,{children:(0,qe.jsx)(Q.l,{children:e("incident.notification.send")})}),(0,qe.jsx)(O.T,{children:(0,qe.jsxs)(B.M,{value:Be,onChange:e=>Ke(e.target.value),children:[(0,qe.jsx)("option",{value:"parent",children:e("incident.notification.parent")}),(0,qe.jsx)("option",{value:"emergency",children:e("incident.notification.emergency")}),(0,qe.jsx)("option",{value:"regulatory",children:e("incident.notification.regulatory")})]})}),(0,qe.jsxs)(F.I,{children:[(0,qe.jsx)(r.Q,{color:"secondary",onClick:()=>We(!1),children:e("common.cancel")}),(0,qe.jsxs)(r.Q,{color:"primary",onClick:async()=>{try{const i=await Ze.X.post("/api/incident/".concat(s,"/notify-parent"),{method:"email",urgency:(null===$||void 0===$?void 0:$.urgency)||"Medium"});i.success?(Le.oR.success(e("incident.notification.sent")),We(!1),ss()):Le.oR.error(i.message||e("incident.notification.error"))}catch(i){Le.oR.error(e("incident.notification.error"))}},children:[(0,qe.jsx)(He.Ay,{icon:re,className:"me-2"}),e("common.send")]})]})]}),(0,qe.jsxs)(_.z,{visible:Xe,onClose:()=>Ge(!1),children:[(0,qe.jsx)(z.E,{children:(0,qe.jsx)(Q.l,{children:e("incident.comments.add")})}),(0,qe.jsx)(O.T,{children:(0,qe.jsx)(Y.I,{rows:4,placeholder:e("incident.comments.placeholder"),value:Ye,onChange:e=>Fe(e.target.value)})}),(0,qe.jsxs)(F.I,{children:[(0,qe.jsx)(r.Q,{color:"secondary",onClick:()=>Ge(!1),children:e("common.cancel")}),(0,qe.jsx)(r.Q,{color:"primary",onClick:async()=>{try{const i=await Ze.X.post("/api/incident/".concat(s,"/comment"),{content:Ye,isInternal:!0});i.success?(Le.oR.success(e("incident.comment.added")),Ge(!1),Fe(""),ss()):Le.oR.error(i.message||e("incident.comment.error"))}catch(i){Le.oR.error(e("incident.comment.error"))}},disabled:!Ye.trim(),children:e("common.add")})]})]}),(0,qe.jsxs)(K.F,{placement:"end",visible:_e,onHide:()=>ze(!1),children:[(0,qe.jsxs)(J.C,{children:[(0,qe.jsx)(ee.z,{children:e("incident.timeline.title")}),(0,qe.jsx)(se.E,{className:"text-reset",onClick:()=>ze(!1)})]}),(0,qe.jsx)(ie.X,{children:$.timeline&&$.timeline.length>0?(0,qe.jsx)("div",{className:"timeline",children:$.timeline.map((e=>(0,qe.jsxs)("div",{className:"timeline-item",children:[(0,qe.jsx)("div",{className:"timeline-marker"}),(0,qe.jsxs)("div",{className:"timeline-content",children:[(0,qe.jsxs)("div",{className:"d-flex justify-content-between align-items-start mb-2",children:[(0,qe.jsx)("strong",{children:e.action}),(0,qe.jsx)("small",{className:"text-muted",children:(0,Ie.GP)((0,Ee.H)(e.timestamp),"dd/MM HH:mm")})]}),(0,qe.jsx)("p",{className:"mb-1",children:e.details}),(0,qe.jsxs)("small",{className:"text-muted",children:["by ",e.user]})]})]},e.id)))}):(0,qe.jsx)("p",{className:"text-muted",children:e("incident.timeline.empty")})})]})]})}},82932:(e,s,i)=>{i.d(s,{Ay:()=>r,n9:()=>l});var n=i(26910);const c={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_API_URL||"/api",t=n.A.create({baseURL:c,headers:{"Content-Type":"application/json"}});t.interceptors.request.use((e=>{const s=localStorage.getItem("token");return s&&(e.headers.Authorization="Bearer ".concat(s)),e})),t.interceptors.response.use((e=>e),(e=>{var s;return 401===(null===(s=e.response)||void 0===s?void 0:s.status)&&(localStorage.removeItem("token"),window.location.href="/login"),Promise.reject(e)}));const r=t,l={getDashboardStats:()=>t.get("/dashboard/stats"),getIncidents:e=>t.get("/incidents",{params:e}),createIncident:e=>t.post("/incidents",e),getIncident:e=>t.get("/incidents/".concat(e)),updateIncident:(e,s)=>t.put("/incidents/".concat(e),s),deleteIncident:e=>t.delete("/incidents/".concat(e)),getRiskMatrix:()=>t.get("/risks/matrix"),getRiskAssessments:e=>t.get("/risks/assessments",{params:e}),createRiskAssessment:e=>t.post("/risks/assessments",e),getRiskAssessment:e=>t.get("/risks/assessments/".concat(e)),updateRiskAssessment:(e,s)=>t.put("/risks/assessments/".concat(e),s),getPermits:e=>t.get("/permits",{params:e}),getActivePermits:()=>t.get("/permits/active"),createPermit:e=>t.post("/permits",e),getPermit:e=>t.get("/permits/".concat(e)),updatePermit:(e,s)=>t.put("/permits/".concat(e),s),approvePermit:e=>t.post("/permits/".concat(e,"/approve")),closePermit:e=>t.post("/permits/".concat(e,"/close")),getTrainingRecords:e=>t.get("/training",{params:e}),getTrainingCompliance:()=>t.get("/training/compliance"),createTrainingRecord:e=>t.post("/training",e),getDocuments:e=>t.get("/documents",{params:e}),uploadDocument:e=>t.post("/documents/upload",e,{headers:{"Content-Type":"multipart/form-data"}}),downloadDocument:e=>t.get("/documents/".concat(e,"/download"),{responseType:"blob"}),getAnalytics:e=>t.get("/analytics",{params:e}),getIncidentTrends:()=>t.get("/analytics/incident-trends"),getRiskHeatmap:()=>t.get("/analytics/risk-heatmap"),getComplianceMetrics:()=>t.get("/analytics/compliance-metrics")}},85826:(e,s,i)=>{i.d(s,{k:()=>n});var n=["512 512","<path fill='var(--ci-primary-color, currentColor)' d='M344,16H168V168H16V344H168V496H344V344H496V168H344ZM464,200V312H312V464H200V312H48V200H200V48H312V200Z' class='ci-primary'/>"]},91834:(e,s,i)=>{i.d(s,{$:()=>n});var n=["512 512","<polygon fill='var(--ci-primary-color, currentColor)' points='368 350.643 256 413.643 144 350.643 144 284.081 112 266.303 112 369.357 256 450.357 400 369.357 400 266.303 368 284.081 368 350.643' class='ci-primary'/><path fill='var(--ci-primary-color, currentColor)' d='M256,45.977,32,162.125v27.734L256,314.3,448,207.637V296h32V162.125ZM416,188.808l-32,17.777L256,277.7,128,206.585,96,188.808,73.821,176.486,256,82.023l182.179,94.463Z' class='ci-primary'/>"]}}]);