"use strict";(self.webpackChunkdigitalhse_web=self.webpackChunkdigitalhse_web||[]).push([[99],{51242:(e,n,s)=>{s.r(n),s.d(n,{default:()=>ge});var a=s(9950),t=s(28429),i=s(64771),c=s(39696);const l=a.lazy((()=>Promise.all([s.e(520),s.e(494),s.e(859)]).then(s.bind(s,42859)))),o=a.lazy((()=>Promise.all([s.e(520),s.e(394),s.e(82),s.e(639),s.e(528),s.e(388)]).then(s.bind(s,77388)))),m=a.lazy((()=>Promise.all([s.e(520),s.e(394),s.e(82),s.e(226),s.e(114)]).then(s.bind(s,89114)))),r=a.lazy((()=>Promise.all([s.e(520),s.e(394),s.e(82),s.e(639),s.e(579),s.e(112)]).then(s.bind(s,82112)))),d=a.lazy((()=>Promise.all([s.e(394),s.e(579),s.e(297),s.e(546)]).then(s.bind(s,91546)))),h=[{path:"/",exact:!0,name:"Home",element:l},{path:"/dashboard",name:"Dashboard",element:l},{path:"/incidents",exact:!0,name:"Incidents",element:o},{path:"/incidents/list",name:"Incident List",element:o},{path:"/incidents/new",name:"Report Incident",element:m},{path:"/incidents/debug",name:"Debug",element:a.lazy((()=>s.e(541).then(s.bind(s,39541))))},{path:"/incidents/test",name:"Test API",element:a.lazy((()=>Promise.all([s.e(520),s.e(870)]).then(s.bind(s,38870))))},{path:"/incidents/:id/investigation",name:"Investigation",element:d},{path:"/incidents/:id",name:"Incident Details",element:r},{path:"/risks/matrix",name:"Risk Matrix",element:a.lazy((()=>s.e(885).then(s.bind(s,65266))))},{path:"/risks/assessments",name:"Risk Assessments",element:a.lazy((()=>s.e(641).then(s.bind(s,84641))))},{path:"/risks/assessments/new",name:"New Risk Assessment",element:a.lazy((()=>s.e(602).then(s.bind(s,52602))))},{path:"/permits",exact:!0,name:"Permits",element:a.lazy((()=>s.e(386).then(s.bind(s,39386))))},{path:"/permits/new",name:"New Permit",element:a.lazy((()=>s.e(364).then(s.bind(s,67364))))},{path:"/training",exact:!0,name:"Training",element:a.lazy((()=>s.e(481).then(s.bind(s,63481))))},{path:"/training/records",name:"Training Records",element:a.lazy((()=>s.e(151).then(s.bind(s,19151))))},{path:"/documents",name:"Documents",element:a.lazy((()=>s.e(689).then(s.bind(s,92689))))},{path:"/compliance",name:"Compliance",element:a.lazy((()=>s.e(231).then(s.bind(s,32231))))},{path:"/analytics",name:"Analytics",element:a.lazy((()=>s.e(831).then(s.bind(s,33831))))}];var x=s(44414);const p=()=>(0,x.jsx)(i.T,{fluid:!0,children:(0,x.jsx)(a.Suspense,{fallback:(0,x.jsx)(c.J,{color:"primary"}),children:(0,x.jsxs)(t.BV,{children:[h.map(((e,n)=>e.element&&(0,x.jsx)(t.qh,{path:e.path,element:(0,x.jsx)(e.element,{})},n))),(0,x.jsx)(t.qh,{path:"/",element:(0,x.jsx)(t.C5,{to:"dashboard",replace:!0})})]})})}),j=a.memo(p);var g=s(99310);const b=()=>(0,x.jsxs)(g.F,{children:[(0,x.jsxs)("div",{children:[(0,x.jsx)("a",{href:"https://www.bsj.sch.id",target:"_blank",rel:"noopener noreferrer",children:"The British School Jakarta"}),(0,x.jsxs)("span",{className:"ms-1",children:["\xa9 ",(new Date).getFullYear()," Digital HSE Management System."]})]}),(0,x.jsxs)("div",{className:"ms-auto",children:[(0,x.jsx)("span",{className:"me-1",children:"Powered by"}),(0,x.jsx)("a",{href:"https://coreui.io/react",target:"_blank",rel:"noopener noreferrer",children:"CoreUI React"})]})]}),y=a.memo(b);var u=s(42074),v=s(10300),N=s(87514),f=s(28122),k=s(24629),A=s(29353),w=s(17831),z=s(45728),C=s(3628),I=s(67583),S=s(35642),P=s(5617),D=s(1093),R=s(40565),H=s(54325),T=s(81949),M=s(3069),q=s(6991),B=s(90863),E=s(78384),J=s(62293),V=s(80687),_=s(25278),F=s(89379),L=s(73962),U=s(79157);const $=()=>{const e=(0,t.zy)().pathname,n=(e=>{const n=[];return e.split("/").reduce(((e,s,a,t)=>{const i="".concat(e,"/").concat(s),c=((e,n)=>{const s=n.find((n=>n.path===e));return s?s.name:""})(i,h);return c&&n.push({pathname:i,name:c,active:a+1===t.length}),i})),n})(e);return(0,x.jsxs)(L.V,{className:"m-0 ms-2",children:[(0,x.jsx)(U.I,{href:"/",children:"Home"}),n.map(((e,n)=>(0,a.createElement)(U.I,(0,F.A)((0,F.A)({},e.active?{active:!0}:{href:e.pathname}),{},{key:n}),e.name)))]})},Q=a.memo($),W=()=>{const e=(0,v.wA)(),n=(0,v.d4)((e=>e.sidebarShow)),s={name:"John Doe",role:"HSE Manager",avatar:"/avatars/default.jpg"},a=e=>{console.log("Changing language to:",e)};return(0,x.jsxs)(N.v,{position:"sticky",className:"mb-4",children:[(0,x.jsxs)(i.T,{fluid:!0,children:[(0,x.jsx)(f.h,{className:"ps-1",onClick:()=>e({type:"set",sidebarShow:!n}),children:(0,x.jsx)(T.Ay,{icon:M.o,size:"lg"})}),(0,x.jsx)(k.C,{className:"mx-auto d-md-none",children:(0,x.jsx)(u.k2,{to:"/",className:"text-decoration-none",children:(0,x.jsx)("h5",{className:"mb-0",children:"Digital HSE"})})}),(0,x.jsxs)(A.q,{className:"d-none d-md-flex me-auto",children:[(0,x.jsx)(w.g,{children:(0,x.jsx)(z.H,{as:u.k2,to:"/dashboard",children:"Dashboard"})}),(0,x.jsx)(w.g,{children:(0,x.jsx)(z.H,{as:u.k2,to:"/incidents/new",children:"Report Incident"})}),(0,x.jsx)(w.g,{children:(0,x.jsx)(z.H,{as:u.k2,to:"/permits/active",children:"Active Permits"})})]}),(0,x.jsxs)(A.q,{children:[(0,x.jsx)(w.g,{children:(0,x.jsxs)(z.H,{href:"#",children:[(0,x.jsx)(T.Ay,{icon:q.k,size:"lg"}),(0,x.jsx)(C.$,{color:"danger",position:"top-end",shape:"pill",children:"5"})]})}),(0,x.jsx)(w.g,{children:(0,x.jsxs)(z.H,{href:"#",children:[(0,x.jsx)(T.Ay,{icon:B.X,size:"lg"}),(0,x.jsx)(C.$,{color:"info",position:"top-end",shape:"pill",children:"3"})]})}),(0,x.jsxs)(I.j,{variant:"nav-item",children:[(0,x.jsx)(S.V,{className:"py-0",caret:!1,children:(0,x.jsx)(T.Ay,{icon:E.j,size:"lg"})}),(0,x.jsxs)(P.Q,{className:"pt-0",children:[(0,x.jsxs)(D.k,{onClick:()=>a("en"),children:[(0,x.jsx)("span",{className:"flag-icon flag-icon-gb me-2"}),"English"]}),(0,x.jsxs)(D.k,{onClick:()=>a("id"),children:[(0,x.jsx)("span",{className:"flag-icon flag-icon-id me-2"}),"Bahasa Indonesia"]})]})]}),(0,x.jsxs)(I.j,{variant:"nav-item",children:[(0,x.jsx)(S.V,{className:"py-0",caret:!1,children:(0,x.jsxs)("div",{className:"avatar-container d-flex align-items-center",children:[(0,x.jsxs)("div",{className:"me-2 d-none d-md-block text-end",children:[(0,x.jsx)("div",{className:"fw-semibold",children:s.name}),(0,x.jsx)("small",{className:"text-medium-emphasis",children:s.role})]}),(0,x.jsx)("div",{className:"avatar bg-primary text-white",children:s.name.split(" ").map((e=>e[0])).join("")})]})}),(0,x.jsxs)(P.Q,{className:"pt-0",children:[(0,x.jsxs)(D.k,{href:"#/profile",children:[(0,x.jsx)(T.Ay,{icon:J.o,className:"me-2"}),"Profile"]}),(0,x.jsxs)(D.k,{href:"#/settings",children:[(0,x.jsx)(T.Ay,{icon:V.e,className:"me-2"}),"Settings"]}),(0,x.jsx)(R.A,{}),(0,x.jsxs)(D.k,{href:"#/logout",children:[(0,x.jsx)(T.Ay,{icon:_.I,className:"me-2"}),"Logout"]})]})]})]})]}),(0,x.jsx)(H.G,{}),(0,x.jsx)(i.T,{fluid:!0,children:(0,x.jsx)(Q,{})})]})};var G=s(85811),O=s(80045),X=s(7713),Y=s(12994),K=s(53986),Z=s(3332);const ee=["component","name","badge","icon","to"],ne=["component","name","icon","to","items"],se=e=>{let{items:n}=e;const s=(0,t.zy)(),a=(e,n)=>{const{component:s,name:a,badge:t,icon:i,to:c}=e;(0,K.A)(e,ee);return(0,x.jsx)(s,{children:(0,x.jsxs)(u.k2,{to:c,className:"nav-link",children:[i&&i,a&&a,t&&(0,x.jsx)(C.$,{color:t.color,className:"ms-auto",children:t.text})]})},n)},i=(e,n)=>{const{component:t,name:c,icon:l,to:o,items:m}=e,r=(0,K.A)(e,ne);return(0,x.jsx)(t,(0,F.A)((0,F.A)({idx:String(n),toggler:(0,x.jsxs)(x.Fragment,{children:[l&&l,c&&c]}),visible:s.pathname.startsWith(o||"/")},r),{},{children:null===m||void 0===m?void 0:m.map(((e,n)=>e.items?i(e,n):a(e,n)))}),n)},c=(e,n)=>e.component===Z.h?((e,n)=>{const{component:s,name:a}=e;return(0,x.jsx)(s,{children:a},n)})(e,n):e.items?i(e,n):a(e,n);return(0,x.jsx)(x.Fragment,{children:n&&n.map(((e,n)=>c(e,n)))})};var ae=s(76144),te=(s(22836),s(5445)),ie=s(97572),ce=s(88859),le=s(5754),oe=s(350),me=s(25152),re=s(22653),de=s(79367),he=s(97721);const xe=[{component:w.g,name:"Dashboard",to:"/dashboard",icon:(0,x.jsx)(T.Ay,{icon:te.I,customClassName:"nav-icon"})},{component:Z.h,name:"Safety Management"},{component:he.q,name:"Incidents",to:"/incidents",icon:(0,x.jsx)(T.Ay,{icon:ie.p,customClassName:"nav-icon"}),items:[{component:w.g,name:"Report Incident",to:"/incidents/new"},{component:w.g,name:"Incident List",to:"/incidents/list"},{component:w.g,name:"Investigations",to:"/incidents"}]},{component:he.q,name:"Risk Management",to:"/risks",icon:(0,x.jsx)(T.Ay,{icon:ce.O,customClassName:"nav-icon"}),items:[{component:w.g,name:"Risk Matrix",to:"/risks/matrix"},{component:w.g,name:"Risk Assessments",to:"/risks/assessments"},{component:w.g,name:"Control Measures",to:"/risks/controls"}]},{component:w.g,name:"Permit to Work",to:"/permits",icon:(0,x.jsx)(T.Ay,{icon:le.x,customClassName:"nav-icon"}),badge:{color:"warning",text:"Active"}},{component:Z.h,name:"Compliance"},{component:w.g,name:"Training",to:"/training",icon:(0,x.jsx)(T.Ay,{icon:oe.P,customClassName:"nav-icon"})},{component:w.g,name:"Documents",to:"/documents",icon:(0,x.jsx)(T.Ay,{icon:me.T,customClassName:"nav-icon"})},{component:w.g,name:"Compliance",to:"/compliance",icon:(0,x.jsx)(T.Ay,{icon:re.l,customClassName:"nav-icon"})},{component:Z.h,name:"Analytics"},{component:w.g,name:"Reports & Analytics",to:"/analytics",icon:(0,x.jsx)(T.Ay,{icon:de.N,customClassName:"nav-icon"})}],pe=()=>{const e=(0,v.wA)(),n=(0,v.d4)((e=>e.sidebarUnfoldable)),s=(0,v.d4)((e=>e.sidebarShow));return(0,x.jsxs)(G._,{position:"fixed",unfoldable:n,visible:s,onVisibleChange:n=>{e({type:"set",sidebarShow:n})},children:[(0,x.jsxs)(O.l,{className:"d-none d-md-flex",children:[(0,x.jsxs)(u.k2,{to:"/",className:"sidebar-brand-full d-flex align-items-center text-decoration-none",children:[(0,x.jsx)("img",{src:"/assets/brand/logo-placeholder.svg",alt:"BSJ Logo",height:40,className:"me-2"}),(0,x.jsxs)("div",{children:[(0,x.jsx)("h5",{className:"text-white mb-0",children:"Digital HSE"}),(0,x.jsx)("small",{className:"text-white-50",children:"The British School Jakarta"})]})]}),(0,x.jsx)("img",{className:"sidebar-brand-narrow",src:"/assets/brand/logo-placeholder.svg",alt:"BSJ",height:35})]}),(0,x.jsx)(X.T,{children:(0,x.jsx)(ae.A,{children:(0,x.jsx)(se,{items:xe})})}),(0,x.jsx)(Y.y,{className:"d-none d-lg-flex",onClick:()=>e({type:"set",sidebarUnfoldable:!n})})]})},je=a.memo(pe);s(67818);const ge=()=>(0,x.jsxs)("div",{className:"sidebar-fixed sidebar-lg-show",children:[(0,x.jsx)(je,{}),(0,x.jsxs)("div",{className:"wrapper d-flex flex-column min-vh-100",children:[(0,x.jsx)(W,{}),(0,x.jsx)("div",{className:"body flex-grow-1 px-3",children:(0,x.jsx)(j,{})}),(0,x.jsx)(y,{})]})]})}}]);