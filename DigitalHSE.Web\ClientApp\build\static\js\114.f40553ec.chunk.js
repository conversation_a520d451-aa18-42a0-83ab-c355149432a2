"use strict";(self.webpackChunkdigitalhse_web=self.webpackChunkdigitalhse_web||[]).push([[114],{79894:(e,i,n)=>{n.d(i,{X:()=>t});var s=n(82932);const t={async get(e){try{return{success:!0,data:(await s.Ay.get(e)).data}}catch(i){return console.error("API GET Error:",i),{success:!1,data:null,message:"Request failed"}}},async post(e,i,n){try{return{success:!0,data:(await s.Ay.post(e,i,n)).data}}catch(t){return console.error("API POST Error:",t),{success:!1,data:null,message:"Request failed"}}},async put(e,i){try{return{success:!0,data:(await s.Ay.put(e,i)).data}}catch(n){return console.error("API PUT Error:",n),{success:!1,data:null,message:"Request failed"}}},async delete(e){try{return{success:!0,data:(await s.Ay.delete(e)).data}}catch(i){return console.error("API DELETE Error:",i),{success:!1,data:null,message:"Request failed"}}}}},82932:(e,i,n)=>{n.d(i,{Ay:()=>a,n9:()=>o});var s=n(26910);const t={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_API_URL||"/api",r=s.A.create({baseURL:t,headers:{"Content-Type":"application/json"}});r.interceptors.request.use((e=>{const i=localStorage.getItem("token");return i&&(e.headers.Authorization="Bearer ".concat(i)),e})),r.interceptors.response.use((e=>e),(e=>{var i;return 401===(null===(i=e.response)||void 0===i?void 0:i.status)&&(localStorage.removeItem("token"),window.location.href="/login"),Promise.reject(e)}));const a=r,o={getDashboardStats:()=>r.get("/dashboard/stats"),getIncidents:e=>r.get("/incidents",{params:e}),createIncident:e=>r.post("/incidents",e),getIncident:e=>r.get("/incidents/".concat(e)),updateIncident:(e,i)=>r.put("/incidents/".concat(e),i),deleteIncident:e=>r.delete("/incidents/".concat(e)),getRiskMatrix:()=>r.get("/risks/matrix"),getRiskAssessments:e=>r.get("/risks/assessments",{params:e}),createRiskAssessment:e=>r.post("/risks/assessments",e),getRiskAssessment:e=>r.get("/risks/assessments/".concat(e)),updateRiskAssessment:(e,i)=>r.put("/risks/assessments/".concat(e),i),getPermits:e=>r.get("/permits",{params:e}),getActivePermits:()=>r.get("/permits/active"),createPermit:e=>r.post("/permits",e),getPermit:e=>r.get("/permits/".concat(e)),updatePermit:(e,i)=>r.put("/permits/".concat(e),i),approvePermit:e=>r.post("/permits/".concat(e,"/approve")),closePermit:e=>r.post("/permits/".concat(e,"/close")),getTrainingRecords:e=>r.get("/training",{params:e}),getTrainingCompliance:()=>r.get("/training/compliance"),createTrainingRecord:e=>r.post("/training",e),getDocuments:e=>r.get("/documents",{params:e}),uploadDocument:e=>r.post("/documents/upload",e,{headers:{"Content-Type":"multipart/form-data"}}),downloadDocument:e=>r.get("/documents/".concat(e,"/download"),{responseType:"blob"}),getAnalytics:e=>r.get("/analytics",{params:e}),getIncidentTrends:()=>r.get("/analytics/incident-trends"),getRiskHeatmap:()=>r.get("/analytics/risk-heatmap"),getComplianceMetrics:()=>r.get("/analytics/compliance-metrics")}},89114:(e,i,n)=>{n.r(i),n.d(i,{default:()=>V});var s=n(89379),t=n(9950),r=n(30578),a=n(13019),o=n(98114),c=n(52684),l=n(71398),d=n(78402),m=n(68852),h=n(77641),u=n(95304),p=n(3380),x=n(85042),j=n(61114),g=n(39696),v=n(3628),f=n(23793),y=n(9134),A=n(64771),b=n(38290),C=n(50025),N=n(49468),T=n(36617),k=n(8572),w=n(58756),U=n(82699),S=n(94926),P=n(63943),I=n(30169),q=n(85826),E=n(91834),R=n(65321),D=n(54732),O=n(97572),M=n(48507),L=n(24642),B=n(2977),F=n(81949),Y=n(67818),z=n(28429),G=n(29714),_=n(85923),W=n(79894),J=n(44414);const Q=[{id:1,title:"incident.form.steps.basic",icon:S.v},{id:2,title:"incident.form.steps.location",icon:P.r},{id:3,title:"incident.form.steps.people",icon:I.g},{id:4,title:"incident.form.steps.response",icon:q.k},{id:5,title:"incident.form.steps.context",icon:E.$},{id:6,title:"incident.form.steps.evidence",icon:R.u}],X={1:G.Ik({incidentDateTime:G.p6().required("Incident date and time is required").max(new Date,"Cannot be in the future"),title:G.Yj().required("Title is required").max(200),description:G.Yj().required("Description is required").max(2e3),category:G.Yj().required("Category is required"),type:G.Yj().required("Type is required"),severity:G.Yj().required("Severity is required"),urgency:G.Yj().required("Urgency is required"),isAnonymous:G.zM()}),2:G.Ik({building:G.Yj().required("Building is required"),location:G.Yj().required("Location is required"),floor:G.Yj().required("Floor is required")}),3:G.Ik({reporterType:G.Yj().required("Reporter type is required"),reportedBy:G.Yj().when("isAnonymous",((e,i)=>!1===e[0]?i.required("Reporter name is required"):i)),reporterEmail:G.Yj().email("Invalid email").required("Email is required"),reporterPhone:G.Yj().matches(/^(\+62|62|0)8[1-9][0-9]{6,9}$/,"Invalid Indonesian phone number")}),4:G.Ik({immediateActions:G.Yj().when(["severity","urgency"],((e,i)=>{let[n,s]=e;return n>="3"||s>="3"?i.required("Immediate actions required for serious incidents"):i}))}),5:G.Ik({parentGuardianContact:G.Yj().when("personAffectedType",((e,i)=>"1"===e[0]?i.required("Parent contact required for student incidents"):i))}),6:G.Ik({photoUrls:G.YO().max(10,"Maximum 10 photos allowed")})},V=()=>{const{t:e,i18n:i}=(0,Y.Bd)(),n=(0,z.Zp)(),[I,q]=(0,t.useState)(!1),[E,R]=(0,t.useState)(1),[V,$]=(0,t.useState)({incidentDateTime:new Date,isAnonymous:!1,emergencyServicesNotified:!1,requiresMedicalAttention:!1,requiresBPJSReporting:!1,requiresMinistryReporting:!1,witnessNames:[],witnessContacts:[],photoUrls:[],videoUrls:[],documentUrls:[],parentPreferredLanguage:i.language}),[H,K]=(0,t.useState)({categories:[],types:[],severities:[],urgencies:[],personTypes:[]}),[Z,ee]=(0,t.useState)(!1),[ie,ne]=(0,t.useState)({}),[se,te]=(0,t.useState)(!1),[re,ae]=(0,t.useState)({photos:[],videos:[],documents:[]});(0,t.useEffect)((()=>{(async()=>{try{console.log("Loading dropdown data...");const[e,i,n,s,t]=await Promise.all([W.X.get("/api/incident/categories"),W.X.get("/api/incident/types"),W.X.get("/api/incident/severities"),W.X.get("/api/incident/urgencies"),W.X.get("/api/incident/person-types")]);console.log("API Responses:",{categories:e,types:i,severities:n,urgencies:s,personTypes:t});const r=e=>e.success&&Array.isArray(e.data)||Array.isArray(e.data)?e.data:Array.isArray(e)?e:[];K({categories:r(e),types:r(i),severities:r(n),urgencies:r(s),personTypes:r(t)})}catch(e){console.error("Failed to load dropdown data:",e),K({categories:[],types:[],severities:[],urgencies:[],personTypes:[]})}})()}),[e]);const oe=async()=>{try{const e=X[E]||G.Ik({});return await e.validate(V,{abortEarly:!1}),ne({}),!0}catch(e){if(e instanceof G.yI){const i={};e.inner.forEach((e=>{e.path&&(i[e.path]=e.message)})),ne(i)}return!1}},ce=(e,i)=>{$((n=>(0,s.A)((0,s.A)({},n),{},{[e]:i}))),ie[e]&&ne((i=>(0,s.A)((0,s.A)({},i),{},{[e]:""})))},le=()=>{te(!0),navigator.geolocation?navigator.geolocation.getCurrentPosition((i=>{ce("latitude",i.coords.latitude),ce("longitude",i.coords.longitude),te(!1),_.oR.success(e("incident.form.location.captured"))}),(i=>{console.error("Error getting location:",i),_.oR.error(e("incident.form.location.error")),te(!1)})):(_.oR.error(e("incident.form.location.notSupported")),te(!1))},de=async(i,n)=>{const t={photos:5242880,videos:52428800,documents:10485760},r=Array.from(i).filter((i=>!(i.size>t[n])||(_.oR.error(e("incident.form.files.tooLarge",{type:n,size:t[n]/1024/1024})),!1)));ae((e=>(0,s.A)((0,s.A)({},e),{},{[n]:[...e[n],...r]})));const a=r.map((e=>URL.createObjectURL(e))),o="".concat(n.slice(0,-1),"Urls");ce(o,[...V[o]||[],...a])},me=(e,i)=>{ae((n=>(0,s.A)((0,s.A)({},n),{},{[i]:n[i].filter(((i,n)=>n!==e))})));const n="".concat(i.slice(0,-1),"Urls");ce(n,V[n].filter(((i,n)=>n!==e)))};(0,t.useEffect)((()=>{const e=setInterval((()=>{localStorage.setItem("incidentDraft",JSON.stringify(V))}),3e4);return()=>clearInterval(e)}),[V]),(0,t.useEffect)((()=>{const i=localStorage.getItem("incidentDraft");if(i)try{const n=JSON.parse(i);$(n),_.oR.info(e("incident.form.draftLoaded"))}catch(n){console.error("Failed to load draft:",n)}}),[e]);return(0,J.jsxs)(A.T,{fluid:!0,children:[(0,J.jsx)(c.s,{children:(0,J.jsxs)(l.U,{lg:12,children:[(0,J.jsx)(r.E,{className:"mb-4",children:(0,J.jsxs)(o.W,{children:[(0,J.jsx)(b.f,{className:"mb-3",children:(0,J.jsxs)(C.E,{value:E/Q.length*100,children:[e("incident.form.step")," ",E," / ",Q.length]})}),(0,J.jsx)("div",{className:"d-flex justify-content-between",children:Q.map((i=>(0,J.jsxs)("div",{className:"text-center ".concat(E>=i.id?"text-primary":"text-muted"),children:[(0,J.jsx)(F.Ay,{icon:i.icon,size:"lg",className:E>=i.id?"text-primary":"text-muted"}),(0,J.jsx)("div",{className:"small mt-1 d-none d-md-block",children:e(i.title)})]},i.id)))})]})}),(()=>{var i,n;switch(E){case 1:return(0,J.jsxs)(r.E,{children:[(0,J.jsx)(a.V,{children:(0,J.jsx)("h4",{children:e("incident.form.basicInfo")})}),(0,J.jsxs)(o.W,{children:[(0,J.jsxs)(c.s,{className:"mb-3",children:[(0,J.jsxs)(l.U,{md:3,children:[(0,J.jsxs)(d.A,{children:[e("incident.form.date")," *"]}),(0,J.jsx)(m.O,{type:"date",value:V.incidentDateTime?new Date(V.incidentDateTime).toISOString().split("T")[0]:"",onChange:e=>{const i=e.target.value,n=V.incidentDateTime?new Date(V.incidentDateTime).toTimeString().split(" ")[0].substring(0,5):"12:00",s=new Date("".concat(i,"T").concat(n));ce("incidentDateTime",s)},max:(new Date).toISOString().split("T")[0],invalid:!!ie.incidentDateTime}),ie.incidentDateTime&&(0,J.jsx)(h.T,{invalid:!0,children:ie.incidentDateTime})]}),(0,J.jsxs)(l.U,{md:3,children:[(0,J.jsxs)(d.A,{children:[e("incident.form.time")," *"]}),(0,J.jsx)(m.O,{type:"time",value:V.incidentDateTime?new Date(V.incidentDateTime).toTimeString().split(" ")[0].substring(0,5):"",onChange:e=>{const i=e.target.value,n=V.incidentDateTime?new Date(V.incidentDateTime).toISOString().split("T")[0]:(new Date).toISOString().split("T")[0],s=new Date("".concat(n,"T").concat(i));ce("incidentDateTime",s)},invalid:!!ie.incidentDateTime})]}),(0,J.jsxs)(l.U,{md:6,children:[(0,J.jsx)(d.A,{children:e("incident.form.anonymous")}),(0,J.jsx)(u.C,{type:"switch",id:"anonymousSwitch",label:e("incident.form.reportAnonymously"),checked:V.isAnonymous,onChange:e=>ce("isAnonymous",e.target.checked)})]})]}),(0,J.jsx)(c.s,{className:"mb-3",children:(0,J.jsxs)(l.U,{md:12,children:[(0,J.jsxs)(d.A,{children:[e("incident.form.title")," *"]}),(0,J.jsx)(m.O,{type:"text",value:V.title||"",onChange:e=>ce("title",e.target.value),invalid:!!ie.title,feedback:ie.title,placeholder:e("incident.form.titlePlaceholder")})]})}),(0,J.jsx)(c.s,{className:"mb-3",children:(0,J.jsxs)(l.U,{md:12,children:[(0,J.jsxs)(d.A,{children:[e("incident.form.description")," *"]}),(0,J.jsx)(p.I,{rows:4,value:V.description||"",onChange:e=>ce("description",e.target.value),invalid:!!ie.description,feedback:ie.description,placeholder:e("incident.form.descriptionPlaceholder")}),(0,J.jsxs)("small",{className:"text-muted",children:[(null===(i=V.description)||void 0===i?void 0:i.length)||0," / 2000"]})]})}),(0,J.jsxs)(c.s,{className:"mb-3",children:[(0,J.jsxs)(l.U,{md:6,children:[(0,J.jsxs)(d.A,{children:[e("incident.form.category")," *"]}),(0,J.jsxs)(x.M,{value:V.category||"",onChange:e=>ce("category",e.target.value),invalid:!!ie.category,feedback:ie.category,children:[(0,J.jsx)("option",{value:"",children:e("common.select")}),Array.isArray(H.categories)&&H.categories.map((e=>(0,J.jsx)("option",{value:e.value,children:e.display},e.value)))]})]}),(0,J.jsxs)(l.U,{md:6,children:[(0,J.jsxs)(d.A,{children:[e("incident.form.type")," *"]}),(0,J.jsxs)(x.M,{value:V.type||"",onChange:e=>ce("type",e.target.value),invalid:!!ie.type,feedback:ie.type,children:[(0,J.jsx)("option",{value:"",children:e("common.select")}),Array.isArray(H.types)&&H.types.map((e=>(0,J.jsx)("option",{value:e.value,children:e.display},e.value)))]})]})]}),(0,J.jsxs)(c.s,{className:"mb-3",children:[(0,J.jsxs)(l.U,{md:6,children:[(0,J.jsxs)(d.A,{children:[e("incident.form.severity")," *"]}),(0,J.jsxs)(x.M,{value:V.severity||"",onChange:e=>ce("severity",e.target.value),invalid:!!ie.severity,feedback:ie.severity,children:[(0,J.jsx)("option",{value:"",children:e("common.select")}),Array.isArray(H.severities)&&H.severities.map((e=>(0,J.jsx)("option",{value:e.value,children:e.display},e.value)))]})]}),(0,J.jsxs)(l.U,{md:6,children:[(0,J.jsxs)(d.A,{children:[e("incident.form.urgency")," *"]}),(0,J.jsxs)(x.M,{value:V.urgency||"",onChange:e=>ce("urgency",e.target.value),invalid:!!ie.urgency,feedback:ie.urgency,children:[(0,J.jsx)("option",{value:"",children:e("common.select")}),Array.isArray(H.urgencies)&&H.urgencies.map((e=>(0,J.jsx)("option",{value:e.value,children:e.display},e.value)))]})]})]})]})]});case 2:return(0,J.jsxs)(r.E,{children:[(0,J.jsx)(a.V,{children:(0,J.jsx)("h4",{children:e("incident.form.locationInfo")})}),(0,J.jsxs)(o.W,{children:[(0,J.jsxs)(c.s,{className:"mb-3",children:[(0,J.jsxs)(l.U,{md:6,children:[(0,J.jsxs)(d.A,{children:[e("incident.form.building")," *"]}),(0,J.jsxs)(x.M,{value:V.building||"",onChange:e=>ce("building",e.target.value),invalid:!!ie.building,feedback:ie.building,children:[(0,J.jsx)("option",{value:"",children:e("common.select")}),(0,J.jsx)("option",{value:"main",children:"Main Building"}),(0,J.jsx)("option",{value:"secondary",children:"Secondary Building"}),(0,J.jsx)("option",{value:"sports",children:"Sports Complex"}),(0,J.jsx)("option",{value:"cafeteria",children:"Cafeteria"}),(0,J.jsx)("option",{value:"library",children:"Library"}),(0,J.jsx)("option",{value:"lab",children:"Laboratory Building"}),(0,J.jsx)("option",{value:"admin",children:"Administration"}),(0,J.jsx)("option",{value:"outdoor",children:"Outdoor Area"})]})]}),(0,J.jsxs)(l.U,{md:6,children:[(0,J.jsxs)(d.A,{children:[e("incident.form.floor")," *"]}),(0,J.jsxs)(x.M,{value:V.floor||"",onChange:e=>ce("floor",e.target.value),invalid:!!ie.floor,feedback:ie.floor,children:[(0,J.jsx)("option",{value:"",children:e("common.select")}),(0,J.jsx)("option",{value:"basement",children:"Basement"}),(0,J.jsx)("option",{value:"ground",children:"Ground Floor"}),(0,J.jsx)("option",{value:"1",children:"1st Floor"}),(0,J.jsx)("option",{value:"2",children:"2nd Floor"}),(0,J.jsx)("option",{value:"3",children:"3rd Floor"}),(0,J.jsx)("option",{value:"4",children:"4th Floor"}),(0,J.jsx)("option",{value:"roof",children:"Roof"})]})]})]}),(0,J.jsxs)(c.s,{className:"mb-3",children:[(0,J.jsxs)(l.U,{md:6,children:[(0,J.jsx)(d.A,{children:e("incident.form.room")}),(0,J.jsx)(m.O,{type:"text",value:V.room||"",onChange:e=>ce("room",e.target.value),placeholder:e("incident.form.roomPlaceholder")})]}),(0,J.jsxs)(l.U,{md:6,children:[(0,J.jsxs)(d.A,{children:[e("incident.form.generalLocation")," *"]}),(0,J.jsx)(m.O,{type:"text",value:V.location||"",onChange:e=>ce("location",e.target.value),invalid:!!ie.location,feedback:ie.location,placeholder:e("incident.form.locationPlaceholder")})]})]}),(0,J.jsx)(c.s,{className:"mb-3",children:(0,J.jsxs)(l.U,{md:12,children:[(0,J.jsx)(d.A,{children:e("incident.form.specificLocation")}),(0,J.jsx)(p.I,{rows:2,value:V.specificLocation||"",onChange:e=>ce("specificLocation",e.target.value),placeholder:e("incident.form.specificLocationPlaceholder")})]})}),(0,J.jsx)(c.s,{className:"mb-3",children:(0,J.jsxs)(l.U,{md:12,children:[(0,J.jsxs)(j.Q,{color:"primary",variant:"outline",onClick:le,disabled:se,children:[se?(0,J.jsx)(g.J,{size:"sm"}):(0,J.jsx)(F.Ay,{icon:P.r})," ",e("incident.form.captureGPS")]}),V.latitude&&V.longitude&&(0,J.jsx)("div",{className:"mt-2",children:(0,J.jsxs)(v.$,{color:"success",children:["GPS: ",V.latitude.toFixed(6),", ",V.longitude.toFixed(6)]})})]})})]})]});case 3:return(0,J.jsxs)(r.E,{children:[(0,J.jsx)(a.V,{children:(0,J.jsx)("h4",{children:e("incident.form.peopleInvolved")})}),(0,J.jsxs)(o.W,{children:[(0,J.jsx)("h5",{className:"mb-3",children:e("incident.form.reporterInfo")}),(0,J.jsxs)(c.s,{className:"mb-3",children:[(0,J.jsxs)(l.U,{md:6,children:[(0,J.jsxs)(d.A,{children:[e("incident.form.reporterType")," *"]}),(0,J.jsxs)(x.M,{value:V.reporterType||"",onChange:e=>ce("reporterType",e.target.value),invalid:!!ie.reporterType,feedback:ie.reporterType,children:[(0,J.jsx)("option",{value:"",children:e("common.select")}),Array.isArray(H.personTypes)&&H.personTypes.map((e=>(0,J.jsx)("option",{value:e.value,children:e.display},e.value)))]})]}),(0,J.jsxs)(l.U,{md:6,children:[(0,J.jsxs)(d.A,{children:[e("incident.form.reporterName")," ",!V.isAnonymous&&"*"]}),(0,J.jsx)(m.O,{type:"text",value:V.isAnonymous?"Anonymous":V.reportedBy||"",onChange:e=>ce("reportedBy",e.target.value),invalid:!!ie.reportedBy,feedback:ie.reportedBy,disabled:V.isAnonymous})]})]}),(0,J.jsxs)(c.s,{className:"mb-3",children:[(0,J.jsxs)(l.U,{md:6,children:[(0,J.jsxs)(d.A,{children:[e("incident.form.reporterEmail")," *"]}),(0,J.jsx)(m.O,{type:"email",value:V.reporterEmail||"",onChange:e=>ce("reporterEmail",e.target.value),invalid:!!ie.reporterEmail,feedback:ie.reporterEmail})]}),(0,J.jsxs)(l.U,{md:6,children:[(0,J.jsx)(d.A,{children:e("incident.form.reporterPhone")}),(0,J.jsx)(m.O,{type:"tel",value:V.reporterPhone||"",onChange:e=>ce("reporterPhone",e.target.value),invalid:!!ie.reporterPhone,feedback:ie.reporterPhone,placeholder:"+62..."})]})]}),(0,J.jsx)(c.s,{className:"mb-3",children:(0,J.jsxs)(l.U,{md:6,children:[(0,J.jsx)(d.A,{children:e("incident.form.reporterDepartment")}),(0,J.jsx)(m.O,{type:"text",value:V.reporterDepartment||"",onChange:e=>ce("reporterDepartment",e.target.value)})]})}),(0,J.jsx)("hr",{}),(0,J.jsx)("h5",{className:"mb-3",children:e("incident.form.personAffected")}),(0,J.jsxs)(c.s,{className:"mb-3",children:[(0,J.jsxs)(l.U,{md:6,children:[(0,J.jsx)(d.A,{children:e("incident.form.personType")}),(0,J.jsxs)(x.M,{value:V.personAffectedType||"",onChange:e=>ce("personAffectedType",e.target.value),children:[(0,J.jsx)("option",{value:"",children:e("common.select")}),Array.isArray(H.personTypes)&&H.personTypes.map((e=>(0,J.jsx)("option",{value:e.value,children:e.display},e.value)))]})]}),(0,J.jsxs)(l.U,{md:6,children:[(0,J.jsx)(d.A,{children:e("incident.form.personName")}),(0,J.jsx)(m.O,{type:"text",value:V.personAffectedName||"",onChange:e=>ce("personAffectedName",e.target.value)})]})]}),"1"===V.personAffectedType&&(0,J.jsx)(J.Fragment,{children:(0,J.jsxs)(c.s,{className:"mb-3",children:[(0,J.jsxs)(l.U,{md:4,children:[(0,J.jsx)(d.A,{children:e("incident.form.studentId")}),(0,J.jsx)(m.O,{type:"text",value:V.personAffectedId||"",onChange:e=>ce("personAffectedId",e.target.value)})]}),(0,J.jsxs)(l.U,{md:4,children:[(0,J.jsx)(d.A,{children:e("incident.form.class")}),(0,J.jsx)(m.O,{type:"text",value:V.personAffectedClass||"",onChange:e=>ce("personAffectedClass",e.target.value)})]}),(0,J.jsxs)(l.U,{md:4,children:[(0,J.jsx)(d.A,{children:e("incident.form.age")}),(0,J.jsx)(m.O,{type:"number",value:V.personAffectedAge||"",onChange:e=>ce("personAffectedAge",parseInt(e.target.value))})]})]})}),(0,J.jsx)("hr",{}),(0,J.jsx)("h5",{className:"mb-3",children:e("incident.form.witnesses")}),(0,J.jsx)(c.s,{className:"mb-3",children:(0,J.jsx)(l.U,{md:12,children:(0,J.jsx)(j.Q,{color:"primary",variant:"outline",size:"sm",onClick:()=>{ce("witnessNames",[...V.witnessNames,""]),ce("witnessContacts",[...V.witnessContacts,""])},children:e("incident.form.addWitness")})})}),null===(n=V.witnessNames)||void 0===n?void 0:n.map(((i,n)=>(0,J.jsxs)(c.s,{className:"mb-2",children:[(0,J.jsx)(l.U,{md:5,children:(0,J.jsx)(m.O,{type:"text",placeholder:e("incident.form.witnessName"),value:V.witnessNames[n],onChange:e=>{const i=[...V.witnessNames];i[n]=e.target.value,ce("witnessNames",i)}})}),(0,J.jsx)(l.U,{md:5,children:(0,J.jsx)(m.O,{type:"text",placeholder:e("incident.form.witnessContact"),value:V.witnessContacts[n],onChange:e=>{const i=[...V.witnessContacts];i[n]=e.target.value,ce("witnessContacts",i)}})}),(0,J.jsx)(l.U,{md:2,children:(0,J.jsx)(j.Q,{color:"danger",variant:"outline",size:"sm",onClick:()=>{ce("witnessNames",V.witnessNames.filter(((e,i)=>i!==n))),ce("witnessContacts",V.witnessContacts.filter(((e,i)=>i!==n)))},children:(0,J.jsx)(F.Ay,{icon:D.X})})})]},n)))]})]});case 4:return(0,J.jsxs)(r.E,{children:[(0,J.jsx)(a.V,{children:(0,J.jsx)("h4",{children:e("incident.form.immediateResponse")})}),(0,J.jsxs)(o.W,{children:[(0,J.jsx)(c.s,{className:"mb-3",children:(0,J.jsxs)(l.U,{md:12,children:[(0,J.jsx)(d.A,{children:e("incident.form.immediateActions")}),(0,J.jsx)(p.I,{rows:3,value:V.immediateActions||"",onChange:e=>ce("immediateActions",e.target.value),invalid:!!ie.immediateActions,feedback:ie.immediateActions,placeholder:e("incident.form.immediateActionsPlaceholder")})]})}),(0,J.jsx)(c.s,{className:"mb-3",children:(0,J.jsxs)(l.U,{md:12,children:[(0,J.jsx)(d.A,{children:e("incident.form.firstAid")}),(0,J.jsx)(p.I,{rows:2,value:V.firstAidProvided||"",onChange:e=>ce("firstAidProvided",e.target.value),placeholder:e("incident.form.firstAidPlaceholder")})]})}),(0,J.jsxs)(c.s,{className:"mb-3",children:[(0,J.jsx)(l.U,{md:6,children:(0,J.jsx)(u.C,{type:"checkbox",id:"emergencyServices",label:e("incident.form.emergencyServicesNotified"),checked:V.emergencyServicesNotified,onChange:e=>ce("emergencyServicesNotified",e.target.checked)})}),V.emergencyServicesNotified&&(0,J.jsx)(l.U,{md:6,children:(0,J.jsxs)(x.M,{value:V.emergencyServiceType||"",onChange:e=>ce("emergencyServiceType",e.target.value),children:[(0,J.jsx)("option",{value:"",children:e("common.select")}),(0,J.jsx)("option",{value:"Police",children:e("incident.form.police")}),(0,J.jsx)("option",{value:"Ambulance",children:e("incident.form.ambulance")}),(0,J.jsx)("option",{value:"Fire",children:e("incident.form.fire")}),(0,J.jsx)("option",{value:"Hospital",children:e("incident.form.hospital")})]})})]}),(0,J.jsx)(c.s,{className:"mb-3",children:(0,J.jsx)(l.U,{md:6,children:(0,J.jsx)(u.C,{type:"checkbox",id:"medicalAttention",label:e("incident.form.requiresMedicalAttention"),checked:V.requiresMedicalAttention,onChange:e=>ce("requiresMedicalAttention",e.target.checked)})})}),V.requiresMedicalAttention&&(0,J.jsxs)(J.Fragment,{children:[(0,J.jsx)(c.s,{className:"mb-3",children:(0,J.jsxs)(l.U,{md:6,children:[(0,J.jsx)(d.A,{children:e("incident.form.hospitalName")}),(0,J.jsx)(m.O,{type:"text",value:V.hospitalName||"",onChange:e=>ce("hospitalName",e.target.value)})]})}),(0,J.jsx)(c.s,{className:"mb-3",children:(0,J.jsxs)(l.U,{md:12,children:[(0,J.jsx)(d.A,{children:e("incident.form.treatmentDetails")}),(0,J.jsx)(p.I,{rows:2,value:V.medicalTreatmentDetails||"",onChange:e=>ce("medicalTreatmentDetails",e.target.value)})]})})]})]})]});case 5:return(0,J.jsxs)(r.E,{children:[(0,J.jsx)(a.V,{children:(0,J.jsx)("h4",{children:e("incident.form.contextInfo")})}),(0,J.jsxs)(o.W,{children:[(0,J.jsx)("h5",{className:"mb-3",children:e("incident.form.schoolContext")}),(0,J.jsxs)(c.s,{className:"mb-3",children:[(0,J.jsxs)(l.U,{md:6,children:[(0,J.jsx)(d.A,{children:e("incident.form.activityType")}),(0,J.jsxs)(x.M,{value:V.activityType||"",onChange:e=>ce("activityType",e.target.value),children:[(0,J.jsx)("option",{value:"",children:e("common.select")}),(0,J.jsx)("option",{value:"classroom",children:"Classroom Lesson"}),(0,J.jsx)("option",{value:"laboratory",children:"Laboratory Activity"}),(0,J.jsx)("option",{value:"sports",children:"Sports/PE"}),(0,J.jsx)("option",{value:"break",children:"Break Time"}),(0,J.jsx)("option",{value:"assembly",children:"Assembly"}),(0,J.jsx)("option",{value:"fieldtrip",children:"Field Trip"}),(0,J.jsx)("option",{value:"afterschool",children:"After School Activity"}),(0,J.jsx)("option",{value:"other",children:"Other"})]})]}),(0,J.jsxs)(l.U,{md:6,children:[(0,J.jsx)(d.A,{children:e("incident.form.subjectClass")}),(0,J.jsx)(m.O,{type:"text",value:V.subjectClass||"",onChange:e=>ce("subjectClass",e.target.value)})]})]}),(0,J.jsxs)(c.s,{className:"mb-3",children:[(0,J.jsxs)(l.U,{md:6,children:[(0,J.jsx)(d.A,{children:e("incident.form.teacherInCharge")}),(0,J.jsx)(m.O,{type:"text",value:V.teacherInCharge||"",onChange:e=>ce("teacherInCharge",e.target.value)})]}),(0,J.jsxs)(l.U,{md:6,children:[(0,J.jsx)(d.A,{children:e("incident.form.supervisorPresent")}),(0,J.jsx)(m.O,{type:"text",value:V.supervisorPresent||"",onChange:e=>ce("supervisorPresent",e.target.value)})]})]}),(0,J.jsxs)(c.s,{className:"mb-3",children:[(0,J.jsxs)(l.U,{md:4,children:[(0,J.jsx)(d.A,{children:e("incident.form.studentsPresent")}),(0,J.jsx)(m.O,{type:"number",value:V.studentsPresent||0,onChange:e=>ce("studentsPresent",parseInt(e.target.value))})]}),(0,J.jsxs)(l.U,{md:4,children:[(0,J.jsx)(d.A,{children:e("incident.form.weather")}),(0,J.jsxs)(x.M,{value:V.weatherConditions||"",onChange:e=>ce("weatherConditions",e.target.value),children:[(0,J.jsx)("option",{value:"",children:e("common.select")}),(0,J.jsx)("option",{value:"sunny",children:"Sunny"}),(0,J.jsx)("option",{value:"cloudy",children:"Cloudy"}),(0,J.jsx)("option",{value:"rainy",children:"Rainy"}),(0,J.jsx)("option",{value:"stormy",children:"Stormy"})]})]}),(0,J.jsxs)(l.U,{md:4,children:[(0,J.jsx)(d.A,{children:e("incident.form.lighting")}),(0,J.jsxs)(x.M,{value:V.lightingConditions||"",onChange:e=>ce("lightingConditions",e.target.value),children:[(0,J.jsx)("option",{value:"",children:e("common.select")}),(0,J.jsx)("option",{value:"good",children:"Good"}),(0,J.jsx)("option",{value:"adequate",children:"Adequate"}),(0,J.jsx)("option",{value:"poor",children:"Poor"}),(0,J.jsx)("option",{value:"dark",children:"Dark"})]})]})]}),"1"===V.personAffectedType&&(0,J.jsxs)(J.Fragment,{children:[(0,J.jsx)("hr",{}),(0,J.jsx)("h5",{className:"mb-3",children:e("incident.form.parentInfo")}),(0,J.jsxs)(c.s,{className:"mb-3",children:[(0,J.jsxs)(l.U,{md:6,children:[(0,J.jsx)(d.A,{children:e("incident.form.parentName")}),(0,J.jsx)(m.O,{type:"text",value:V.parentGuardianName||"",onChange:e=>ce("parentGuardianName",e.target.value)})]}),(0,J.jsxs)(l.U,{md:6,children:[(0,J.jsxs)(d.A,{children:[e("incident.form.parentContact")," *"]}),(0,J.jsx)(m.O,{type:"text",value:V.parentGuardianContact||"",onChange:e=>ce("parentGuardianContact",e.target.value),invalid:!!ie.parentGuardianContact,feedback:ie.parentGuardianContact})]})]}),(0,J.jsxs)(c.s,{className:"mb-3",children:[(0,J.jsxs)(l.U,{md:6,children:[(0,J.jsx)(d.A,{children:e("incident.form.parentEmail")}),(0,J.jsx)(m.O,{type:"email",value:V.parentGuardianEmail||"",onChange:e=>ce("parentGuardianEmail",e.target.value)})]}),(0,J.jsxs)(l.U,{md:6,children:[(0,J.jsx)(d.A,{children:e("incident.form.preferredLanguage")}),(0,J.jsxs)(x.M,{value:V.parentPreferredLanguage||"en",onChange:e=>ce("parentPreferredLanguage",e.target.value),children:[(0,J.jsx)("option",{value:"en",children:"English"}),(0,J.jsx)("option",{value:"id",children:"Bahasa Indonesia"})]})]})]})]})]})]});case 6:return(0,J.jsxs)(r.E,{children:[(0,J.jsx)(a.V,{children:(0,J.jsx)("h4",{children:e("incident.form.evidenceCompliance")})}),(0,J.jsxs)(o.W,{children:[(0,J.jsx)("h5",{className:"mb-3",children:e("incident.form.evidence")}),(0,J.jsx)(c.s,{className:"mb-3",children:(0,J.jsxs)(l.U,{md:12,children:[(0,J.jsxs)(d.A,{children:[e("incident.form.photos")," (",re.photos.length,"/10)"]}),(0,J.jsx)(m.O,{type:"file",multiple:!0,accept:"image/*",onChange:e=>e.target.files&&de(e.target.files,"photos"),disabled:re.photos.length>=10}),ie.photoUrls&&(0,J.jsx)("div",{className:"invalid-feedback d-block",children:ie.photoUrls}),(0,J.jsx)("div",{className:"mt-2",children:re.photos.map(((e,i)=>(0,J.jsxs)(v.$,{color:"info",className:"me-2 mb-2",children:[e.name,(0,J.jsx)(f.E,{size:"sm",onClick:()=>me(i,"photos"),className:"ms-2"})]},i)))})]})}),(0,J.jsx)(c.s,{className:"mb-3",children:(0,J.jsxs)(l.U,{md:12,children:[(0,J.jsxs)(d.A,{children:[e("incident.form.videos")," (",re.videos.length,"/5)"]}),(0,J.jsx)(m.O,{type:"file",multiple:!0,accept:"video/*",onChange:e=>e.target.files&&de(e.target.files,"videos"),disabled:re.videos.length>=5}),(0,J.jsx)("div",{className:"mt-2",children:re.videos.map(((e,i)=>(0,J.jsxs)(v.$,{color:"info",className:"me-2 mb-2",children:[e.name,(0,J.jsx)(f.E,{size:"sm",onClick:()=>me(i,"videos"),className:"ms-2"})]},i)))})]})}),(0,J.jsx)(c.s,{className:"mb-3",children:(0,J.jsxs)(l.U,{md:12,children:[(0,J.jsxs)(d.A,{children:[e("incident.form.documents")," (",re.documents.length,"/10)"]}),(0,J.jsx)(m.O,{type:"file",multiple:!0,accept:".pdf,.doc,.docx,.xls,.xlsx",onChange:e=>e.target.files&&de(e.target.files,"documents"),disabled:re.documents.length>=10}),(0,J.jsx)("div",{className:"mt-2",children:re.documents.map(((e,i)=>(0,J.jsxs)(v.$,{color:"info",className:"me-2 mb-2",children:[e.name,(0,J.jsx)(f.E,{size:"sm",onClick:()=>me(i,"documents"),className:"ms-2"})]},i)))})]})}),(0,J.jsx)("hr",{}),(0,J.jsx)("h5",{className:"mb-3",children:e("incident.form.regulatoryCompliance")}),(0,J.jsxs)(c.s,{className:"mb-3",children:[(0,J.jsx)(l.U,{md:6,children:(0,J.jsx)(u.C,{type:"checkbox",id:"bpjsReporting",label:e("incident.form.requiresBPJS"),checked:V.requiresBPJSReporting,onChange:e=>ce("requiresBPJSReporting",e.target.checked)})}),(0,J.jsx)(l.U,{md:6,children:(0,J.jsx)(u.C,{type:"checkbox",id:"ministryReporting",label:e("incident.form.requiresMinistry"),checked:V.requiresMinistryReporting,onChange:e=>ce("requiresMinistryReporting",e.target.checked)})})]}),(V.requiresBPJSReporting||V.requiresMinistryReporting)&&(0,J.jsxs)(y.k,{color:"warning",children:[(0,J.jsx)(F.Ay,{icon:O.p,className:"me-2"}),e("incident.form.regulatoryWarning")]})]})]});default:return null}})(),(0,J.jsx)(r.E,{className:"mt-4",children:(0,J.jsx)(o.W,{children:(0,J.jsxs)("div",{className:"d-flex justify-content-between",children:[(0,J.jsx)("div",{children:E>1&&(0,J.jsxs)(j.Q,{color:"secondary",variant:"outline",onClick:()=>{E>1&&(R((e=>e-1)),window.scrollTo(0,0))},children:[(0,J.jsx)(F.Ay,{icon:M.T})," ",e("common.previous")]})}),(0,J.jsxs)("div",{children:[(0,J.jsx)(j.Q,{color:"secondary",variant:"outline",className:"me-2",onClick:()=>n("/incidents"),children:e("common.cancel")}),E<Q.length?(0,J.jsxs)(j.Q,{color:"primary",onClick:async()=>{await oe()&&E<Q.length&&(R((e=>e+1)),window.scrollTo(0,0))},children:[e("common.next")," ",(0,J.jsx)(F.Ay,{icon:L.C})]}):(0,J.jsxs)(J.Fragment,{children:[(0,J.jsxs)(j.Q,{color:"info",variant:"outline",className:"me-2",onClick:()=>ee(!0),children:[(0,J.jsx)(F.Ay,{icon:S.v})," ",e("common.preview")]}),(0,J.jsx)(j.Q,{color:"success",onClick:async()=>{if(await oe()){q(!0);try{const s=V.isAnonymous?"/api/incident/anonymous":"/api/incident",t=await W.X.post(s,V);var i;if(t.success)_.oR.success(e("incident.form.success")),V.isAnonymous&&null!==(i=t.data)&&void 0!==i&&i.incidentNumber&&_.oR.info(e("incident.form.trackingCode",{code:t.data.incidentNumber}),{autoClose:!1}),n("/incidents");else _.oR.error(t.message||e("incident.form.error"))}catch(s){console.error("Failed to submit incident:",s),_.oR.error(e("incident.form.error"))}finally{q(!1)}}},disabled:I,children:I?(0,J.jsx)(g.J,{size:"sm"}):(0,J.jsxs)(J.Fragment,{children:[(0,J.jsx)(F.Ay,{icon:B.j})," ",e("common.submit")]})})]})]})]})})})]})}),(0,J.jsxs)(N.z,{visible:Z,onClose:()=>ee(!1),size:"xl",scrollable:!0,children:[(0,J.jsx)(T.E,{children:(0,J.jsx)(k.l,{children:e("incident.form.previewTitle")})}),(0,J.jsx)(w.T,{children:(0,J.jsx)("pre",{children:JSON.stringify(V,null,2)})}),(0,J.jsx)(U.I,{children:(0,J.jsx)(j.Q,{color:"secondary",onClick:()=>ee(!1),children:e("common.close")})})]})]})}}}]);