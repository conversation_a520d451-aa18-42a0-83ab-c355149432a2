﻿// <auto-generated />
using System;
using DigitalHSE.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace DigitalHSE.Infrastructure.Migrations
{
    [DbContext(typeof(DigitalHSEDBContext))]
    partial class DigitalHSEDBContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("public")
                .HasAnnotation("ProductVersion", "8.0.4")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("DigitalHSE.Domain.Auth.Permission", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Action")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Module")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.HasIndex("Module", "Action");

                    b.ToTable("Permissions", "public");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Action = "View",
                            Description = "View incidents",
                            Module = "Incidents",
                            Name = "incidents.view"
                        },
                        new
                        {
                            Id = 2,
                            Action = "Create",
                            Description = "Create new incidents",
                            Module = "Incidents",
                            Name = "incidents.create"
                        },
                        new
                        {
                            Id = 3,
                            Action = "Update",
                            Description = "Update existing incidents",
                            Module = "Incidents",
                            Name = "incidents.update"
                        },
                        new
                        {
                            Id = 4,
                            Action = "Delete",
                            Description = "Delete incidents",
                            Module = "Incidents",
                            Name = "incidents.delete"
                        },
                        new
                        {
                            Id = 5,
                            Action = "Investigate",
                            Description = "Investigate incidents",
                            Module = "Incidents",
                            Name = "incidents.investigate"
                        },
                        new
                        {
                            Id = 6,
                            Action = "Approve",
                            Description = "Approve incident reports",
                            Module = "Incidents",
                            Name = "incidents.approve"
                        },
                        new
                        {
                            Id = 7,
                            Action = "Report",
                            Description = "Generate incident reports",
                            Module = "Incidents",
                            Name = "incidents.report"
                        },
                        new
                        {
                            Id = 8,
                            Action = "View",
                            Description = "View risk assessments",
                            Module = "Risks",
                            Name = "risks.view"
                        },
                        new
                        {
                            Id = 9,
                            Action = "Create",
                            Description = "Create new risk assessments",
                            Module = "Risks",
                            Name = "risks.create"
                        },
                        new
                        {
                            Id = 10,
                            Action = "Update",
                            Description = "Update existing risk assessments",
                            Module = "Risks",
                            Name = "risks.update"
                        },
                        new
                        {
                            Id = 11,
                            Action = "Delete",
                            Description = "Delete risk assessments",
                            Module = "Risks",
                            Name = "risks.delete"
                        },
                        new
                        {
                            Id = 12,
                            Action = "Approve",
                            Description = "Approve risk assessments",
                            Module = "Risks",
                            Name = "risks.approve"
                        },
                        new
                        {
                            Id = 13,
                            Action = "Review",
                            Description = "Review risk assessments",
                            Module = "Risks",
                            Name = "risks.review"
                        },
                        new
                        {
                            Id = 14,
                            Action = "View",
                            Description = "View permits",
                            Module = "Permits",
                            Name = "permits.view"
                        },
                        new
                        {
                            Id = 15,
                            Action = "Create",
                            Description = "Create new permits",
                            Module = "Permits",
                            Name = "permits.create"
                        },
                        new
                        {
                            Id = 16,
                            Action = "Update",
                            Description = "Update existing permits",
                            Module = "Permits",
                            Name = "permits.update"
                        },
                        new
                        {
                            Id = 17,
                            Action = "Delete",
                            Description = "Delete permits",
                            Module = "Permits",
                            Name = "permits.delete"
                        },
                        new
                        {
                            Id = 18,
                            Action = "Approve",
                            Description = "Approve permits",
                            Module = "Permits",
                            Name = "permits.approve"
                        },
                        new
                        {
                            Id = 19,
                            Action = "Issue",
                            Description = "Issue permits",
                            Module = "Permits",
                            Name = "permits.issue"
                        },
                        new
                        {
                            Id = 20,
                            Action = "View",
                            Description = "View training records",
                            Module = "Training",
                            Name = "training.view"
                        },
                        new
                        {
                            Id = 21,
                            Action = "Create",
                            Description = "Create new training records",
                            Module = "Training",
                            Name = "training.create"
                        },
                        new
                        {
                            Id = 22,
                            Action = "Update",
                            Description = "Update existing training records",
                            Module = "Training",
                            Name = "training.update"
                        },
                        new
                        {
                            Id = 23,
                            Action = "Delete",
                            Description = "Delete training records",
                            Module = "Training",
                            Name = "training.delete"
                        },
                        new
                        {
                            Id = 24,
                            Action = "Approve",
                            Description = "Approve training records",
                            Module = "Training",
                            Name = "training.approve"
                        },
                        new
                        {
                            Id = 25,
                            Action = "View",
                            Description = "View compliance items",
                            Module = "Compliance",
                            Name = "compliance.view"
                        },
                        new
                        {
                            Id = 26,
                            Action = "Create",
                            Description = "Create new compliance items",
                            Module = "Compliance",
                            Name = "compliance.create"
                        },
                        new
                        {
                            Id = 27,
                            Action = "Update",
                            Description = "Update existing compliance items",
                            Module = "Compliance",
                            Name = "compliance.update"
                        },
                        new
                        {
                            Id = 28,
                            Action = "Delete",
                            Description = "Delete compliance items",
                            Module = "Compliance",
                            Name = "compliance.delete"
                        },
                        new
                        {
                            Id = 29,
                            Action = "View",
                            Description = "View reports",
                            Module = "Reports",
                            Name = "reports.view"
                        },
                        new
                        {
                            Id = 30,
                            Action = "Create",
                            Description = "Create new reports",
                            Module = "Reports",
                            Name = "reports.create"
                        },
                        new
                        {
                            Id = 31,
                            Action = "View",
                            Description = "View analytics",
                            Module = "Analytics",
                            Name = "analytics.view"
                        },
                        new
                        {
                            Id = 32,
                            Action = "View",
                            Description = "View dashboard",
                            Module = "Dashboard",
                            Name = "dashboard.view"
                        },
                        new
                        {
                            Id = 33,
                            Action = "View",
                            Description = "View users",
                            Module = "Users",
                            Name = "users.view"
                        },
                        new
                        {
                            Id = 34,
                            Action = "Create",
                            Description = "Create new users",
                            Module = "Users",
                            Name = "users.create"
                        },
                        new
                        {
                            Id = 35,
                            Action = "Update",
                            Description = "Update existing users",
                            Module = "Users",
                            Name = "users.update"
                        },
                        new
                        {
                            Id = 36,
                            Action = "Delete",
                            Description = "Delete users",
                            Module = "Users",
                            Name = "users.delete"
                        },
                        new
                        {
                            Id = 37,
                            Action = "ManageRoles",
                            Description = "Manage user roles",
                            Module = "Users",
                            Name = "users.manage_roles"
                        },
                        new
                        {
                            Id = 38,
                            Action = "Settings",
                            Description = "Manage system settings",
                            Module = "System",
                            Name = "system.settings"
                        },
                        new
                        {
                            Id = 39,
                            Action = "Audit",
                            Description = "View system audit logs",
                            Module = "System",
                            Name = "system.audit"
                        },
                        new
                        {
                            Id = 40,
                            Action = "Backup",
                            Description = "Manage system backups",
                            Module = "System",
                            Name = "system.backup"
                        });
                });

            modelBuilder.Entity("DigitalHSE.Domain.Auth.Role", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsSystemRole")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Roles", "public");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "System",
                            Description = "Full system access with all permissions",
                            IsActive = true,
                            IsSystemRole = true,
                            Name = "System Administrator",
                            UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            UpdatedBy = "System"
                        },
                        new
                        {
                            Id = 2,
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "System",
                            Description = "HSE department manager with full HSE module access",
                            IsActive = true,
                            IsSystemRole = true,
                            Name = "HSE Manager",
                            UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            UpdatedBy = "System"
                        },
                        new
                        {
                            Id = 3,
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "System",
                            Description = "HSE officer with operational HSE access",
                            IsActive = true,
                            IsSystemRole = true,
                            Name = "HSE Officer",
                            UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            UpdatedBy = "System"
                        },
                        new
                        {
                            Id = 4,
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "System",
                            Description = "School teacher with incident reporting access",
                            IsActive = true,
                            IsSystemRole = true,
                            Name = "Teacher",
                            UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            UpdatedBy = "System"
                        },
                        new
                        {
                            Id = 5,
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "System",
                            Description = "Student with limited reporting access",
                            IsActive = true,
                            IsSystemRole = true,
                            Name = "Student",
                            UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            UpdatedBy = "System"
                        });
                });

            modelBuilder.Entity("DigitalHSE.Domain.Auth.RolePermission", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("GrantedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<int>("PermissionId")
                        .HasColumnType("integer");

                    b.Property<int>("RoleId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("PermissionId");

                    b.HasIndex("RoleId", "PermissionId")
                        .IsUnique();

                    b.ToTable("RolePermissions", "public");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 1,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 2,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 2,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 3,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 3,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 4,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 4,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 5,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 5,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 6,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 6,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 7,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 7,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 8,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 8,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 9,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 9,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 10,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 10,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 11,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 11,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 12,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 12,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 13,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 13,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 14,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 14,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 15,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 15,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 16,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 16,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 17,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 17,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 18,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 18,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 19,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 19,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 20,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 20,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 21,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 21,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 22,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 22,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 23,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 23,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 24,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 24,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 25,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 25,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 26,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 26,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 27,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 27,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 28,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 28,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 29,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 29,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 30,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 30,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 31,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 31,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 32,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 32,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 33,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 33,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 34,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 34,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 35,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 35,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 36,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 36,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 37,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 37,
                            RoleId = 1
                        },
                        new
                        {
                            Id = 38,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 1,
                            RoleId = 2
                        },
                        new
                        {
                            Id = 39,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 2,
                            RoleId = 2
                        },
                        new
                        {
                            Id = 40,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 3,
                            RoleId = 2
                        },
                        new
                        {
                            Id = 41,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 4,
                            RoleId = 2
                        },
                        new
                        {
                            Id = 42,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 5,
                            RoleId = 2
                        },
                        new
                        {
                            Id = 43,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 6,
                            RoleId = 2
                        },
                        new
                        {
                            Id = 44,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 7,
                            RoleId = 2
                        },
                        new
                        {
                            Id = 45,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 8,
                            RoleId = 2
                        },
                        new
                        {
                            Id = 46,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 9,
                            RoleId = 2
                        },
                        new
                        {
                            Id = 47,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 10,
                            RoleId = 2
                        },
                        new
                        {
                            Id = 48,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 11,
                            RoleId = 2
                        },
                        new
                        {
                            Id = 49,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 12,
                            RoleId = 2
                        },
                        new
                        {
                            Id = 50,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 13,
                            RoleId = 2
                        },
                        new
                        {
                            Id = 51,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 14,
                            RoleId = 2
                        },
                        new
                        {
                            Id = 52,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 15,
                            RoleId = 2
                        },
                        new
                        {
                            Id = 53,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 16,
                            RoleId = 2
                        },
                        new
                        {
                            Id = 54,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 17,
                            RoleId = 2
                        },
                        new
                        {
                            Id = 55,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 18,
                            RoleId = 2
                        },
                        new
                        {
                            Id = 56,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 19,
                            RoleId = 2
                        },
                        new
                        {
                            Id = 57,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 20,
                            RoleId = 2
                        },
                        new
                        {
                            Id = 58,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 21,
                            RoleId = 2
                        },
                        new
                        {
                            Id = 59,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 22,
                            RoleId = 2
                        },
                        new
                        {
                            Id = 60,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 23,
                            RoleId = 2
                        },
                        new
                        {
                            Id = 61,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 24,
                            RoleId = 2
                        },
                        new
                        {
                            Id = 62,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 25,
                            RoleId = 2
                        },
                        new
                        {
                            Id = 63,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 26,
                            RoleId = 2
                        },
                        new
                        {
                            Id = 64,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 27,
                            RoleId = 2
                        },
                        new
                        {
                            Id = 65,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 28,
                            RoleId = 2
                        },
                        new
                        {
                            Id = 66,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 29,
                            RoleId = 2
                        },
                        new
                        {
                            Id = 67,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 30,
                            RoleId = 2
                        },
                        new
                        {
                            Id = 68,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 31,
                            RoleId = 2
                        },
                        new
                        {
                            Id = 69,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 32,
                            RoleId = 2
                        },
                        new
                        {
                            Id = 70,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 1,
                            RoleId = 3
                        },
                        new
                        {
                            Id = 71,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 2,
                            RoleId = 3
                        },
                        new
                        {
                            Id = 72,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 3,
                            RoleId = 3
                        },
                        new
                        {
                            Id = 73,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 5,
                            RoleId = 3
                        },
                        new
                        {
                            Id = 74,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 8,
                            RoleId = 3
                        },
                        new
                        {
                            Id = 75,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 9,
                            RoleId = 3
                        },
                        new
                        {
                            Id = 76,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 10,
                            RoleId = 3
                        },
                        new
                        {
                            Id = 77,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 14,
                            RoleId = 3
                        },
                        new
                        {
                            Id = 78,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 15,
                            RoleId = 3
                        },
                        new
                        {
                            Id = 79,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 16,
                            RoleId = 3
                        },
                        new
                        {
                            Id = 80,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 17,
                            RoleId = 3
                        },
                        new
                        {
                            Id = 81,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 20,
                            RoleId = 3
                        },
                        new
                        {
                            Id = 82,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 21,
                            RoleId = 3
                        },
                        new
                        {
                            Id = 83,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 22,
                            RoleId = 3
                        },
                        new
                        {
                            Id = 84,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 24,
                            RoleId = 3
                        },
                        new
                        {
                            Id = 85,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 25,
                            RoleId = 3
                        },
                        new
                        {
                            Id = 86,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 26,
                            RoleId = 3
                        },
                        new
                        {
                            Id = 87,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 27,
                            RoleId = 3
                        },
                        new
                        {
                            Id = 88,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 28,
                            RoleId = 3
                        },
                        new
                        {
                            Id = 89,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 29,
                            RoleId = 3
                        },
                        new
                        {
                            Id = 90,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 30,
                            RoleId = 3
                        },
                        new
                        {
                            Id = 91,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 31,
                            RoleId = 3
                        },
                        new
                        {
                            Id = 92,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 32,
                            RoleId = 3
                        },
                        new
                        {
                            Id = 93,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 1,
                            RoleId = 4
                        },
                        new
                        {
                            Id = 94,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 2,
                            RoleId = 4
                        },
                        new
                        {
                            Id = 95,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 8,
                            RoleId = 4
                        },
                        new
                        {
                            Id = 96,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 15,
                            RoleId = 4
                        },
                        new
                        {
                            Id = 97,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 25,
                            RoleId = 4
                        },
                        new
                        {
                            Id = 98,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 29,
                            RoleId = 4
                        },
                        new
                        {
                            Id = 99,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 32,
                            RoleId = 4
                        },
                        new
                        {
                            Id = 100,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 1,
                            RoleId = 5
                        },
                        new
                        {
                            Id = 101,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 2,
                            RoleId = 5
                        },
                        new
                        {
                            Id = 102,
                            GrantedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            PermissionId = 32,
                            RoleId = 5
                        });
                });

            modelBuilder.Entity("DigitalHSE.Domain.Auth.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Department")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("EmployeeId")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<int>("FailedLoginAttempts")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsEmailConfirmed")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("LastLoginDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("LockoutEndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Position")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("PreferredLanguage")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(5)
                        .HasColumnType("character varying(5)")
                        .HasDefaultValue("en");

                    b.Property<string>("RefreshToken")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime?>("RefreshTokenExpiryTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.HasIndex("EmployeeId")
                        .IsUnique();

                    b.HasIndex("RefreshToken");

                    b.HasIndex("Username")
                        .IsUnique();

                    b.ToTable("Users", "public");
                });

            modelBuilder.Entity("DigitalHSE.Domain.Auth.UserRole", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("AssignedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<int>("RoleId")
                        .HasColumnType("integer");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.HasIndex("UserId", "RoleId")
                        .IsUnique();

                    b.ToTable("UserRoles", "public");
                });

            modelBuilder.Entity("DigitalHSE.Domain.HSE.Entities.ComplianceItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ComplianceCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ComplianceFrequency")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("CorrectiveActionDeadline")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CorrectiveActions")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("DocumentUrls")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("EffectiveDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("EvidenceProvided")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("EvidenceRequired")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("GovernmentReportingFrequency")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("IndonesianRegulationName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsInViolation")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsIndonesianRegulation")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastAuditDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastAuditResult")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastCompletedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("MinistryDepartment")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("NextDueDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Regulation")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RegulationSection")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RegulatoryBody")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RequirementDetails")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("RequiresBPJSCompliance")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequiresGovernmentReporting")
                        .HasColumnType("boolean");

                    b.Property<string>("ResponsibleDepartment")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ResponsibleEmail")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ResponsiblePerson")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("ViolationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ViolationDetails")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("ComplianceItems", "public");
                });

            modelBuilder.Entity("DigitalHSE.Domain.HSE.Entities.Incident", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AgeGroup")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("AttachmentUrls")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("BPJSReferenceNumber")
                        .HasColumnType("text");

                    b.Property<DateTime?>("BPJSReportedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CorrectiveActions")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Department")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ImmediateActions")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("IncidentDateTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("IncidentNumber")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("InvestigatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("InvestigationCompletedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("MedicalFacility")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("MinistryReferenceNumber")
                        .HasColumnType("text");

                    b.Property<DateTime?>("MinistryReportedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ParentContact")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("ParentNotificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("ParentNotified")
                        .HasColumnType("boolean");

                    b.Property<string>("PhotoUrls")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("PreventiveActions")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("RegulatoryDeadline")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ReportedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("ReportedDateTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ReporterEmail")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ReporterPhone")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("RequiresBPJSReporting")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequiresMedicalAttention")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequiresMinistryReporting")
                        .HasColumnType("boolean");

                    b.Property<string>("RootCause")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Severity")
                        .HasColumnType("integer");

                    b.Property<string>("SpecificLocation")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("StudentClass")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("StudentId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("StudentName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TeacherInCharge")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Witnesses")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Incidents", "public");
                });

            modelBuilder.Entity("DigitalHSE.Domain.HSE.Entities.IncidentCAPA", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ActionNumber")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<decimal>("ActualCost")
                        .HasColumnType("numeric");

                    b.Property<string>("AssignedByName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("AssignedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("AssignedTo")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("CompletionDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CompletionEvidence")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("CostNotes")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("DueDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("EffectivenessNotes")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<decimal>("EstimatedCost")
                        .HasColumnType("numeric");

                    b.Property<int>("IncidentId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsEffective")
                        .HasColumnType("boolean");

                    b.Property<int>("Priority")
                        .HasColumnType("integer");

                    b.Property<string>("ProgressNotes")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("ProgressPercentage")
                        .HasColumnType("integer");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("VerificationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("VerifiedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("IncidentId");

                    b.ToTable("IncidentCAPA", "public");
                });

            modelBuilder.Entity("DigitalHSE.Domain.HSE.Entities.IncidentInvestigation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("ActualCompletionDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ContributingFactors")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("EvidenceCollected")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ImmediateActions")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("IncidentId")
                        .HasColumnType("integer");

                    b.Property<string>("InitialFindings")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("InterviewNotes")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("LeadInvestigator")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("LongTermActions")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Methodology")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("PreventiveMeasures")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ProgressNotes")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("ProgressPercentage")
                        .HasColumnType("integer");

                    b.Property<string>("RootCauses")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ShortTermActions")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("SystemicIssues")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("TargetCompletionDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("TeamMembers")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TechnicalExperts")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("WitnessStatements")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("IncidentId");

                    b.ToTable("IncidentInvestigation", "public");
                });

            modelBuilder.Entity("DigitalHSE.Domain.HSE.Entities.IncidentNotification", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("DeliveredTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ErrorMessage")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("IncidentId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsRegulatoryRequired")
                        .HasColumnType("boolean");

                    b.Property<string>("Language")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("MaxRetries")
                        .HasColumnType("integer");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("NotificationType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Priority")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("ReadTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("RecipientContact")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RecipientName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RecipientRole")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RegulatoryBody")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("RegulatoryDeadline")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("RetryCount")
                        .HasColumnType("integer");

                    b.Property<DateTime>("ScheduledTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("SentTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("IncidentId");

                    b.ToTable("IncidentNotification", "public");
                });

            modelBuilder.Entity("DigitalHSE.Domain.HSE.Entities.Permit", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("ActualEndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ActualStartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ApprovalComments")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ApprovedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("ApprovedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("AttachmentUrls")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ClosedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("ClosedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ClosureComments")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Contractor")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ControlMeasures")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Department")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("EmergencyProcedures")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Equipment")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("EstimatedWorkers")
                        .HasColumnType("integer");

                    b.Property<string>("Hazards")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("IsolationDetails")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("K3ApprovalNumber")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("PermitNumber")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("PreparedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("PreparedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("RequestedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("RequestedEndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("RequestedStartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("RequestorEmail")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RequestorPhone")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RequiredPPE")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("RequiresIsolation")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequiresK3Approval")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequiresSIKB")
                        .HasColumnType("boolean");

                    b.Property<string>("ReviewedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("ReviewedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("RiskAssessmentId")
                        .HasColumnType("integer");

                    b.Property<string>("SIKBNumber")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("Supervisor")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("SupervisorPhone")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("WorkCompleted")
                        .HasColumnType("boolean");

                    b.Property<string>("WorkLocation")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("WorkScope")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Permits", "public");
                });

            modelBuilder.Entity("DigitalHSE.Domain.HSE.Entities.RiskAssessment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Activity")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("AdditionalControls")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ApprovedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("ApprovedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("AssessedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("AssessmentDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("AssessmentNumber")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("AttachmentUrls")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Department")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ExistingControls")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("GovernmentNotificationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Hazards")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("ImplementationDeadline")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("InitialConsequence")
                        .HasColumnType("integer");

                    b.Property<int>("InitialLikelihood")
                        .HasColumnType("integer");

                    b.Property<int>("InitialRiskLevel")
                        .HasColumnType("integer");

                    b.Property<int>("InitialRiskScore")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsApproved")
                        .HasColumnType("boolean");

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("NextReviewDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("PotentialConsequences")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RegulatoryRequirements")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("RequiresGovernmentNotification")
                        .HasColumnType("boolean");

                    b.Property<int>("ResidualConsequence")
                        .HasColumnType("integer");

                    b.Property<int>("ResidualLikelihood")
                        .HasColumnType("integer");

                    b.Property<int>("ResidualRiskLevel")
                        .HasColumnType("integer");

                    b.Property<int>("ResidualRiskScore")
                        .HasColumnType("integer");

                    b.Property<string>("ResponsiblePerson")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("ReviewDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("RiskAssessments", "public");
                });

            modelBuilder.Entity("DigitalHSE.Domain.HSE.Entities.TrainingRecord", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AttendanceUrl")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("CertificateNumber")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("CertificateUrl")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("CompletionDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Department")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<TimeSpan>("Duration")
                        .HasColumnType("interval");

                    b.Property<string>("EmployeeEmail")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("EmployeeId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("EmployeeName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Instructor")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsCompleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsRegulatoryRequired")
                        .HasColumnType("boolean");

                    b.Property<string>("KemnakerNumber")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("MaterialUrls")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("Passed")
                        .HasColumnType("boolean");

                    b.Property<string>("Position")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RecordNumber")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("RefresherFrequencyMonths")
                        .HasColumnType("integer");

                    b.Property<string>("RegulatoryReference")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("RequiresKemnaker")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequiresRefresher")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequiresSKKK")
                        .HasColumnType("boolean");

                    b.Property<string>("SKKKNumber")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<decimal?>("Score")
                        .HasColumnType("numeric");

                    b.Property<string>("TrainingCategory")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("TrainingDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("TrainingProvider")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TrainingTitle")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TrainingType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("TrainingRecords", "public");
                });

            modelBuilder.Entity("DigitalHSE.Domain.Auth.RolePermission", b =>
                {
                    b.HasOne("DigitalHSE.Domain.Auth.Permission", "Permission")
                        .WithMany("RolePermissions")
                        .HasForeignKey("PermissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("DigitalHSE.Domain.Auth.Role", "Role")
                        .WithMany("RolePermissions")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Permission");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("DigitalHSE.Domain.Auth.UserRole", b =>
                {
                    b.HasOne("DigitalHSE.Domain.Auth.Role", "Role")
                        .WithMany("UserRoles")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("DigitalHSE.Domain.Auth.User", "User")
                        .WithMany("UserRoles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("DigitalHSE.Domain.HSE.Entities.IncidentCAPA", b =>
                {
                    b.HasOne("DigitalHSE.Domain.HSE.Entities.Incident", "Incident")
                        .WithMany("CAPAs")
                        .HasForeignKey("IncidentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Incident");
                });

            modelBuilder.Entity("DigitalHSE.Domain.HSE.Entities.IncidentInvestigation", b =>
                {
                    b.HasOne("DigitalHSE.Domain.HSE.Entities.Incident", "Incident")
                        .WithMany("Investigations")
                        .HasForeignKey("IncidentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Incident");
                });

            modelBuilder.Entity("DigitalHSE.Domain.HSE.Entities.IncidentNotification", b =>
                {
                    b.HasOne("DigitalHSE.Domain.HSE.Entities.Incident", "Incident")
                        .WithMany("Notifications")
                        .HasForeignKey("IncidentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Incident");
                });

            modelBuilder.Entity("DigitalHSE.Domain.Auth.Permission", b =>
                {
                    b.Navigation("RolePermissions");
                });

            modelBuilder.Entity("DigitalHSE.Domain.Auth.Role", b =>
                {
                    b.Navigation("RolePermissions");

                    b.Navigation("UserRoles");
                });

            modelBuilder.Entity("DigitalHSE.Domain.Auth.User", b =>
                {
                    b.Navigation("UserRoles");
                });

            modelBuilder.Entity("DigitalHSE.Domain.HSE.Entities.Incident", b =>
                {
                    b.Navigation("CAPAs");

                    b.Navigation("Investigations");

                    b.Navigation("Notifications");
                });
#pragma warning restore 612, 618
        }
    }
}
