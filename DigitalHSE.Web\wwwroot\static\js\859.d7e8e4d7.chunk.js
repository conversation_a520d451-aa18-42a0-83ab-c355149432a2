"use strict";(self.webpackChunkdigitalhse_web=self.webpackChunkdigitalhse_web||[]).push([[859],{42859:(e,t,s)=>{s.r(t),s.d(t,{default:()=>u});var n=s(9950),a=s(52684),i=s(71398),r=s(24411),o=s(30578),c=s(13019),d=s(98114),l=s(13898);var m=s(82932),p=s(44414);const u=()=>{var e,t,s;const{data:u,loading:g}=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{autoFetch:s=!0,onSuccess:a,onError:i}=t,[r,o]=(0,n.useState)(null),[c,d]=(0,n.useState)(!1),[l,m]=(0,n.useState)(null),p=(0,n.useCallback)((async()=>{d(!0),m(null);try{const t=await e();o(t.data),a&&a(t.data)}catch(t){const e=t;m(e),i&&i(e)}finally{d(!1)}}),[e,a,i]);return(0,n.useEffect)((()=>{s&&p()}),[s,p]),{data:r,loading:c,error:l,refetch:p}}((()=>m.n9.getDashboardStats()));return g?(0,p.jsx)("div",{children:"Loading dashboard..."}):(0,p.jsxs)("div",{className:"dashboard-content",children:[(0,p.jsxs)(a.s,{children:[(0,p.jsx)(i.U,{sm:6,lg:3,children:(0,p.jsx)(r.U,{className:"mb-4 hse-stat-widget",color:"danger",value:(null===u||void 0===u?void 0:u.openIncidents)||"0",title:"Open Incidents",chart:(0,p.jsx)("div",{className:"chart-wrapper",style:{height:"70px"},children:(0,p.jsx)(l.WE,{className:"mt-3 mx-3",style:{height:"70px"},data:{labels:(null===u||void 0===u||null===(e=u.incidentTrend)||void 0===e?void 0:e.labels)||[],datasets:[{label:"Incidents",backgroundColor:"transparent",borderColor:"rgba(255,255,255,.55)",pointBackgroundColor:"#c62828",data:(null===u||void 0===u||null===(t=u.incidentTrend)||void 0===t?void 0:t.data)||[]}]},options:{animation:{duration:0},responsive:!0,plugins:{legend:{display:!1}},maintainAspectRatio:!1,scales:{x:{display:!1},y:{display:!1}},elements:{line:{borderWidth:2},point:{radius:0,hitRadius:10,hoverRadius:4}}}})})})}),(0,p.jsx)(i.U,{sm:6,lg:3,children:(0,p.jsx)(r.U,{className:"mb-4 hse-stat-widget",color:"warning",value:(null===u||void 0===u?void 0:u.overdueActions)||"0",title:"Overdue Actions"})}),(0,p.jsx)(i.U,{sm:6,lg:3,children:(0,p.jsx)(r.U,{className:"mb-4 hse-stat-widget",color:"success",value:"".concat((null===u||void 0===u?void 0:u.trainingCompliance)||0,"%"),title:"Training Compliance"})}),(0,p.jsx)(i.U,{sm:6,lg:3,children:(0,p.jsx)(r.U,{className:"mb-4 hse-stat-widget",color:"info",value:(null===u||void 0===u?void 0:u.activePermits)||"0",title:"Active Permits"})})]}),(0,p.jsxs)(a.s,{children:[(0,p.jsx)(i.U,{xs:12,md:6,children:(0,p.jsxs)(o.E,{className:"mb-4",children:[(0,p.jsx)(c.V,{children:"Risk Distribution"}),(0,p.jsx)(d.W,{children:(0,p.jsx)(l.aq,{data:{labels:["Low","Medium","High","Extreme"],datasets:[{data:(null===u||void 0===u?void 0:u.riskDistribution)||[0,0,0,0],backgroundColor:["#689f38","#fbc02d","#f57c00","#c62828"],hoverBackgroundColor:["#558b2f","#f9a825","#ef6c00","#b71c1c"]}]},options:{animation:{duration:0},responsive:!0,plugins:{legend:{position:"bottom"}}}})})]})}),(0,p.jsx)(i.U,{xs:12,md:6,children:(0,p.jsxs)(o.E,{className:"mb-4",children:[(0,p.jsx)(c.V,{children:"Incidents by Department"}),(0,p.jsx)(d.W,{children:(0,p.jsx)(l.BB,{data:{labels:(null===u||void 0===u?void 0:u.departments)||[],datasets:[{label:"Incidents",backgroundColor:"#1b5e20",data:(null===u||void 0===u?void 0:u.departmentIncidents)||[]}]},options:{animation:{duration:0},responsive:!0,plugins:{legend:{display:!1}},scales:{y:{beginAtZero:!0}}}})})]})})]}),(0,p.jsx)(a.s,{children:(0,p.jsx)(i.U,{xs:12,children:(0,p.jsxs)(o.E,{className:"mb-4",children:[(0,p.jsx)(c.V,{children:"Recent Safety Observations"}),(0,p.jsx)(d.W,{children:(0,p.jsx)("div",{className:"table-responsive",children:(0,p.jsxs)("table",{className:"table table-hover",children:[(0,p.jsx)("thead",{children:(0,p.jsxs)("tr",{children:[(0,p.jsx)("th",{children:"Date"}),(0,p.jsx)("th",{children:"Location"}),(0,p.jsx)("th",{children:"Type"}),(0,p.jsx)("th",{children:"Observer"}),(0,p.jsx)("th",{children:"Status"})]})}),(0,p.jsx)("tbody",{children:null===u||void 0===u||null===(s=u.recentObservations)||void 0===s?void 0:s.map(((e,t)=>(0,p.jsxs)("tr",{children:[(0,p.jsx)("td",{children:new Date(e.date).toLocaleDateString()}),(0,p.jsx)("td",{children:e.location}),(0,p.jsx)("td",{children:e.type}),(0,p.jsx)("td",{children:e.observer}),(0,p.jsx)("td",{children:(0,p.jsx)("span",{className:"badge bg-".concat("Open"===e.status?"warning":"success"),children:e.status})})]},t)))})]})})})]})})})]})}},82932:(e,t,s)=>{s.d(t,{Ay:()=>r,n9:()=>o});var n=s(26910);const a={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_API_URL||"/api",i=n.A.create({baseURL:a,headers:{"Content-Type":"application/json"}});i.interceptors.request.use((e=>{const t=localStorage.getItem("token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e})),i.interceptors.response.use((e=>e),(e=>{var t;return 401===(null===(t=e.response)||void 0===t?void 0:t.status)&&(localStorage.removeItem("token"),window.location.href="/login"),Promise.reject(e)}));const r=i,o={getDashboardStats:()=>i.get("/dashboard/stats"),getIncidents:e=>i.get("/incidents",{params:e}),createIncident:e=>i.post("/incidents",e),getIncident:e=>i.get("/incidents/".concat(e)),updateIncident:(e,t)=>i.put("/incidents/".concat(e),t),deleteIncident:e=>i.delete("/incidents/".concat(e)),getRiskMatrix:()=>i.get("/risks/matrix"),getRiskAssessments:e=>i.get("/risks/assessments",{params:e}),createRiskAssessment:e=>i.post("/risks/assessments",e),getRiskAssessment:e=>i.get("/risks/assessments/".concat(e)),updateRiskAssessment:(e,t)=>i.put("/risks/assessments/".concat(e),t),getPermits:e=>i.get("/permits",{params:e}),getActivePermits:()=>i.get("/permits/active"),createPermit:e=>i.post("/permits",e),getPermit:e=>i.get("/permits/".concat(e)),updatePermit:(e,t)=>i.put("/permits/".concat(e),t),approvePermit:e=>i.post("/permits/".concat(e,"/approve")),closePermit:e=>i.post("/permits/".concat(e,"/close")),getTrainingRecords:e=>i.get("/training",{params:e}),getTrainingCompliance:()=>i.get("/training/compliance"),createTrainingRecord:e=>i.post("/training",e),getDocuments:e=>i.get("/documents",{params:e}),uploadDocument:e=>i.post("/documents/upload",e,{headers:{"Content-Type":"multipart/form-data"}}),downloadDocument:e=>i.get("/documents/".concat(e,"/download"),{responseType:"blob"}),getAnalytics:e=>i.get("/analytics",{params:e}),getIncidentTrends:()=>i.get("/analytics/incident-trends"),getRiskHeatmap:()=>i.get("/analytics/risk-heatmap"),getComplianceMetrics:()=>i.get("/analytics/compliance-metrics")}}}]);