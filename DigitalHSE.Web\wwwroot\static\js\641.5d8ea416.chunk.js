"use strict";(self.webpackChunkdigitalhse_web=self.webpackChunkdigitalhse_web||[]).push([[641],{13019:(e,r,s)=>{s.d(r,{V:()=>d});var a=s(3035),l=s(9950),t=s(11942),c=s.n(t),n=s(69344),d=(0,l.forwardRef)((function(e,r){var s=e.children,t=e.as,c=void 0===t?"div":t,d=e.className,o=(0,a.Tt)(e,["children","as","className"]);return l.createElement(c,(0,a.Cl)({className:(0,n.A)("card-header",d)},o,{ref:r}),s)}));d.propTypes={as:c().elementType,children:c().node,className:c().string},d.displayName="CCardHeader"},30578:(e,r,s)=>{s.d(r,{E:()=>o});var a=s(3035),l=s(9950),t=s(11942),c=s.n(t),n=s(69344),d=s(3319),o=(0,l.forwardRef)((function(e,r){var s,t=e.children,c=e.className,d=e.color,o=e.textBgColor,i=e.textColor,m=(0,a.Tt)(e,["children","className","color","textBgColor","textColor"]);return l.createElement("div",(0,a.Cl)({className:(0,n.A)("card",(s={},s["bg-".concat(d)]=d,s["text-".concat(i)]=i,s["text-bg-".concat(o)]=o,s),c)},m,{ref:r}),t)}));o.propTypes={children:c().node,className:c().string,color:d.TX,textBgColor:d.TX,textColor:c().string},o.displayName="CCard"},84641:(e,r,s)=>{s.r(r),s.d(r,{default:()=>n});s(9950);var a=s(30578),l=s(13019),t=s(98114),c=s(44414);const n=()=>(0,c.jsxs)(a.E,{children:[(0,c.jsx)(l.V,{children:(0,c.jsx)("strong",{children:"Risk Assessments"})}),(0,c.jsx)(t.W,{children:(0,c.jsx)("p",{children:"Risk assessments list - To be implemented"})})]})},98114:(e,r,s)=>{s.d(r,{W:()=>d});var a=s(3035),l=s(9950),t=s(11942),c=s.n(t),n=s(69344),d=(0,l.forwardRef)((function(e,r){var s=e.children,t=e.className,c=(0,a.Tt)(e,["children","className"]);return l.createElement("div",(0,a.Cl)({className:(0,n.A)("card-body",t)},c,{ref:r}),s)}));d.propTypes={children:c().node,className:c().string},d.displayName="CCardBody"}}]);