"use strict";(self.webpackChunkdigitalhse_web=self.webpackChunkdigitalhse_web||[]).push([[541],{13019:(e,r,n)=>{n.d(r,{V:()=>o});var s=n(3035),c=n(9950),a=n(11942),t=n.n(a),l=n(69344),o=(0,c.forwardRef)((function(e,r){var n=e.children,a=e.as,t=void 0===a?"div":a,o=e.className,i=(0,s.Tt)(e,["children","as","className"]);return c.createElement(t,(0,s.Cl)({className:(0,l.A)("card-header",o)},i,{ref:r}),n)}));o.propTypes={as:t().elementType,children:t().node,className:t().string},o.displayName="CCardHeader"},30578:(e,r,n)=>{n.d(r,{E:()=>i});var s=n(3035),c=n(9950),a=n(11942),t=n.n(a),l=n(69344),o=n(3319),i=(0,c.forwardRef)((function(e,r){var n,a=e.children,t=e.className,o=e.color,i=e.textBgColor,d=e.textColor,h=(0,s.Tt)(e,["children","className","color","textBgColor","textColor"]);return c.createElement("div",(0,s.Cl)({className:(0,l.A)("card",(n={},n["bg-".concat(o)]=o,n["text-".concat(d)]=d,n["text-bg-".concat(i)]=i,n),t)},h,{ref:r}),a)}));i.propTypes={children:t().node,className:t().string,color:o.TX,textBgColor:o.TX,textColor:t().string},i.displayName="CCard"},39541:(e,r,n)=>{n.r(r),n.d(r,{default:()=>d});n(9950);var s=n(64771),c=n(30578),a=n(13019),t=n(98114),l=n(61114),o=n(28429),i=n(44414);const d=()=>{const e=(0,o.Zp)();return console.log("IncidentDebug component mounted"),(0,i.jsx)(s.T,{children:(0,i.jsxs)(c.E,{children:[(0,i.jsx)(a.V,{children:(0,i.jsx)("h3",{children:"Incident Debug Page"})}),(0,i.jsxs)(t.W,{children:[(0,i.jsx)("p",{children:"This is a test page to verify routing is working."}),(0,i.jsxs)("div",{className:"d-grid gap-2",children:[(0,i.jsx)(l.Q,{color:"primary",onClick:()=>e("/incidents/new"),children:"Go to Incident Form"}),(0,i.jsx)(l.Q,{color:"secondary",onClick:()=>e("/incidents/list"),children:"Go to Incident List"}),(0,i.jsx)(l.Q,{color:"info",onClick:()=>e("/incidents"),children:"Go to Incidents"})]})]})]})})}},98114:(e,r,n)=>{n.d(r,{W:()=>o});var s=n(3035),c=n(9950),a=n(11942),t=n.n(a),l=n(69344),o=(0,c.forwardRef)((function(e,r){var n=e.children,a=e.className,t=(0,s.Tt)(e,["children","className"]);return c.createElement("div",(0,s.Cl)({className:(0,l.A)("card-body",a)},t,{ref:r}),n)}));o.propTypes={children:t().node,className:t().string},o.displayName="CCardBody"}}]);