"use strict";(self.webpackChunkdigitalhse_web=self.webpackChunkdigitalhse_web||[]).push([[885],{8236:(e,r,l)=>{l.d(r,{w:()=>i});var o=l(3035),n=l(9950),s=l(11942),a=l.n(s),t=l(69344),c=l(3319),i=(0,n.forwardRef)((function(e,r){var l,s=e.children,a=e.className,c=e.color,i=(0,o.Tt)(e,["children","className","color"]);return n.createElement("thead",(0,o.Cl)({className:(0,t.A)((l={},l["table-".concat(c)]=c,l),a)||void 0},i,{ref:r}),s)}));i.propTypes={children:a().node,className:a().string,color:c.TX},i.displayName="CTableHead"},13019:(e,r,l)=>{l.d(r,{V:()=>c});var o=l(3035),n=l(9950),s=l(11942),a=l.n(s),t=l(69344),c=(0,n.forwardRef)((function(e,r){var l=e.children,s=e.as,a=void 0===s?"div":s,c=e.className,i=(0,o.Tt)(e,["children","as","className"]);return n.createElement(a,(0,o.Cl)({className:(0,t.A)("card-header",c)},i,{ref:r}),l)}));c.propTypes={as:a().elementType,children:a().node,className:a().string},c.displayName="CCardHeader"},14778:(e,r,l)=>{l.d(r,{c:()=>i});var o=l(3035),n=l(9950),s=l(11942),a=l.n(s),t=l(69344),c=l(3319),i=(0,n.forwardRef)((function(e,r){var l,s=e.children,a=e.active,c=e.align,i=e.className,d=e.color,p=(0,o.Tt)(e,["children","active","align","className","color"]),m=p.scope?"th":"td";return n.createElement(m,(0,o.Cl)({className:(0,t.A)((l={},l["align-".concat(c)]=c,l["table-active"]=a,l["table-".concat(d)]=d,l),i)||void 0},p,{ref:r}),s)}));i.propTypes={active:a().bool,align:a().oneOf(["bottom","middle","top"]),children:a().node,className:a().string,color:c.TX},i.displayName="CTableDataCell"},30578:(e,r,l)=>{l.d(r,{E:()=>i});var o=l(3035),n=l(9950),s=l(11942),a=l.n(s),t=l(69344),c=l(3319),i=(0,n.forwardRef)((function(e,r){var l,s=e.children,a=e.className,c=e.color,i=e.textBgColor,d=e.textColor,p=(0,o.Tt)(e,["children","className","color","textBgColor","textColor"]);return n.createElement("div",(0,o.Cl)({className:(0,t.A)("card",(l={},l["bg-".concat(c)]=c,l["text-".concat(d)]=d,l["text-bg-".concat(i)]=i,l),a)},p,{ref:r}),s)}));i.propTypes={children:a().node,className:a().string,color:c.TX,textBgColor:c.TX,textColor:a().string},i.displayName="CCard"},33652:(e,r,l)=>{l.d(r,{Y:()=>i});var o=l(3035),n=l(9950),s=l(11942),a=l.n(s),t=l(69344),c=l(3319),i=(0,n.forwardRef)((function(e,r){var l,s=e.children,a=e.active,c=e.align,i=e.className,d=e.color,p=(0,o.Tt)(e,["children","active","align","className","color"]);return n.createElement("tr",(0,o.Cl)({className:(0,t.A)((l={},l["align-".concat(c)]=c,l["table-active"]=a,l["table-".concat(d)]=d,l),i)||void 0},p,{ref:r}),s)}));i.propTypes={active:a().bool,align:a().oneOf(["bottom","middle","top"]),children:a().node,className:a().string,color:c.TX},i.displayName="CTableRow"},40121:(e,r,l)=>{l.d(r,{_:()=>y});var o=l(3035),n=l(9950),s=l(11942),a=l.n(s),t=l(69344),c=l(8236),i=l(67111),d=l(63898),p=l(14778),m=l(33652),h=l(3319),u=(0,n.forwardRef)((function(e,r){var l,s=e.children,a=e.className,c=e.color,i=(0,o.Tt)(e,["children","className","color"]);return n.createElement("tfoot",(0,o.Cl)({className:(0,t.A)((l={},l["table-".concat(c)]=c,l),a)||void 0},i,{ref:r}),s)}));u.propTypes={children:a().node,className:a().string,color:h.TX},u.displayName="CTableFoot";var f=(0,n.forwardRef)((function(e,r){var l=e.children,s=(0,o.Tt)(e,["children"]);return n.createElement("caption",(0,o.Cl)({},s,{ref:r}),l)}));f.propTypes={children:a().node},f.displayName="CTableCaption";var b=function(e){var r=e.children,l=e.responsive,s=(0,o.Tt)(e,["children","responsive"]);return l?n.createElement("div",(0,o.Cl)({className:"boolean"===typeof l?"table-responsive":"table-responsive-".concat(l)},s),r):n.createElement(n.Fragment,null,r)};b.propTypes={children:a().node,responsive:a().oneOfType([a().bool,a().oneOf(["sm","md","lg","xl","xxl"])])},b.displayName="CTableResponsiveWrapper";var x=function(e){return e.replace(/[-_.]/g," ").replace(/ +/g," ").replace(/([a-z0-9])([A-Z])/g,"$1 $2").split(" ").map((function(e){return e.charAt(0).toUpperCase()+e.slice(1)})).join(" ")},g=function(e){return Object.keys(e[0]||{}).filter((function(e){return"_"!==e.charAt(0)}))},y=(0,n.forwardRef)((function(e,r){var l,s=e.children,a=e.align,h=e.borderColor,y=e.bordered,C=e.borderless,v=e.caption,N=e.captionTop,j=e.className,T=e.color,k=e.columns,E=e.footer,w=e.hover,O=e.items,R=e.responsive,A=e.small,_=e.striped,M=e.stripedColumns,P=e.tableFootProps,X=e.tableHeadProps,Y=(0,o.Tt)(e,["children","align","borderColor","bordered","borderless","caption","captionTop","className","color","columns","footer","hover","items","responsive","small","striped","stripedColumns","tableFootProps","tableHeadProps"]),H=(0,n.useMemo)((function(){return function(e,r){return e?e.map((function(e){return"object"===typeof e?e.key:e})):r&&g(r)}(k,O)}),[k,O]);return n.createElement(b,{responsive:R},n.createElement("table",(0,o.Cl)({className:(0,t.A)("table",(l={},l["align-".concat(a)]=a,l["border-".concat(h)]=h,l["caption-top"]=N||"top"===v,l["table-bordered"]=y,l["table-borderless"]=C,l["table-".concat(T)]=T,l["table-hover"]=w,l["table-sm"]=A,l["table-striped"]=_,l["table-striped-columns"]=M,l),j)},Y,{ref:r}),(v&&"top"!==v||N)&&n.createElement(f,null,v||N),k&&n.createElement(c.w,(0,o.Cl)({},X),n.createElement(m.Y,null,k.map((function(e,r){return n.createElement(i.$,(0,o.Cl)({},e._props&&(0,o.Cl)({},e._props),e._style&&{style:(0,o.Cl)({},e._style)},{key:r}),function(e){var r;return"object"===typeof e?null!==(r=e.label)&&void 0!==r?r:x(e.key):x(e)}(e))})))),O&&n.createElement(d.C,null,O.map((function(e,r){return n.createElement(m.Y,(0,o.Cl)({},e._props&&(0,o.Cl)({},e._props),{key:r}),H&&H.map((function(r,l){return void 0!==e[r]?n.createElement(p.c,(0,o.Cl)({},e._cellProps&&(0,o.Cl)((0,o.Cl)({},e._cellProps.all&&(0,o.Cl)({},e._cellProps.all)),e._cellProps[r]&&(0,o.Cl)({},e._cellProps[r])),{key:l}),e[r]):null})))}))),s,E&&n.createElement(u,(0,o.Cl)({},P),n.createElement(m.Y,null,E.map((function(e,r){return n.createElement(p.c,(0,o.Cl)({},"object"===typeof e&&e._props&&(0,o.Cl)({},e._props),{key:r}),"object"===typeof e?e.label:e)}))))))}));y.propTypes={align:a().oneOf(["bottom","middle","top"]),borderColor:a().string,bordered:a().bool,borderless:a().bool,caption:a().oneOfType([a().string,a().oneOf(["top"])]),captionTop:a().string,children:a().node,className:a().string,color:h.TX,columns:a().array,footer:a().array,hover:a().bool,items:a().array,responsive:a().oneOfType([a().bool,a().oneOf(["sm","md","lg","xl","xxl"])]),small:a().bool,striped:a().bool,stripedColumns:a().bool,tableFootProps:a().shape((0,o.Cl)({},u.propTypes)),tableHeadProps:a().shape((0,o.Cl)({},c.w.propTypes))},y.displayName="CTable"},52684:(e,r,l)=>{l.d(r,{s:()=>i});var o=l(3035),n=l(9950),s=l(11942),a=l.n(s),t=l(69344),c=["xxl","xl","lg","md","sm","xs"],i=(0,n.forwardRef)((function(e,r){var l=e.children,s=e.className,a=(0,o.Tt)(e,["children","className"]),i=[];return c.forEach((function(e){var r=a[e];delete a[e];var l="xs"===e?"":"-".concat(e);"object"===typeof r&&(r.cols&&i.push("row-cols".concat(l,"-").concat(r.cols)),"number"===typeof r.gutter&&i.push("g".concat(l,"-").concat(r.gutter)),"number"===typeof r.gutterX&&i.push("gx".concat(l,"-").concat(r.gutterX)),"number"===typeof r.gutterY&&i.push("gy".concat(l,"-").concat(r.gutterY)))})),n.createElement("div",(0,o.Cl)({className:(0,t.A)("row",i,s)},a,{ref:r}),l)})),d=a().shape({cols:a().oneOfType([a().oneOf(["auto"]),a().number,a().string]),gutter:a().oneOfType([a().string,a().number]),gutterX:a().oneOfType([a().string,a().number]),gutterY:a().oneOfType([a().string,a().number])});i.propTypes={children:a().node,className:a().string,xs:d,sm:d,md:d,lg:d,xl:d,xxl:d},i.displayName="CRow"},63898:(e,r,l)=>{l.d(r,{C:()=>i});var o=l(3035),n=l(9950),s=l(11942),a=l.n(s),t=l(69344),c=l(3319),i=(0,n.forwardRef)((function(e,r){var l,s=e.children,a=e.className,c=e.color,i=(0,o.Tt)(e,["children","className","color"]);return n.createElement("tbody",(0,o.Cl)({className:(0,t.A)((l={},l["table-".concat(c)]=c,l),a)||void 0},i,{ref:r}),s)}));i.propTypes={children:a().node,className:a().string,color:c.TX},i.displayName="CTableBody"},65266:(e,r,l)=>{l.r(r),l.d(r,{default:()=>b});l(9950);var o=l(52684),n=l(71398),s=l(30578),a=l(13019),t=l(98114),c=l(40121),i=l(8236),d=l(33652),p=l(67111),m=l(63898),h=l(14778),u=l(44414);const f=e=>{let{onCellClick:r,selectedCell:l}=e;const o=["Insignificant","Minor","Moderate","Major","Severe"],n=["Rare","Unlikely","Possible","Likely","Almost Certain"];return(0,u.jsxs)(s.E,{children:[(0,u.jsx)(a.V,{children:(0,u.jsx)("strong",{children:"Risk Assessment Matrix"})}),(0,u.jsxs)(t.W,{children:[(0,u.jsxs)(c._,{bordered:!0,className:"hse-risk-matrix text-center",children:[(0,u.jsx)(i.w,{children:(0,u.jsxs)(d.Y,{children:[(0,u.jsx)(p.$,{children:"Likelihood \\ Consequence"}),o.map(((e,r)=>(0,u.jsx)(p.$,{children:e},r)))]})}),(0,u.jsx)(m.C,{children:n.map(((e,s)=>(0,u.jsxs)(d.Y,{children:[(0,u.jsx)(p.$,{children:e}),o.map(((e,o)=>{const a=n.length-s,t=o+1,c=((e,r)=>{const l=e*r;return l>=20?"risk-extreme":l>=15?"risk-high":l>=10?"risk-medium":"risk-low"})(a,t),i=((e,r)=>{const l=e*r;return l>=20?"Extreme":l>=15?"High":l>=10?"Medium":"Low"})(a,t),d=((e,r)=>(null===l||void 0===l?void 0:l.likelihood)===e&&(null===l||void 0===l?void 0:l.consequence)===r)(a,t);return(0,u.jsxs)(h.c,{className:"".concat(c," ").concat(d?"selected":""),onClick:()=>r&&r(a,t),style:{cursor:r?"pointer":"default",fontWeight:d?"bold":"normal",border:d?"3px solid #333":void 0},children:[i,(0,u.jsx)("br",{}),(0,u.jsxs)("small",{children:["(",a*t,")"]})]},o)}))]},s)))})]}),(0,u.jsxs)("div",{className:"mt-3",children:[(0,u.jsx)("span",{className:"badge risk-low me-2",children:"Low (1-5)"}),(0,u.jsx)("span",{className:"badge risk-medium me-2",children:"Medium (6-12)"}),(0,u.jsx)("span",{className:"badge risk-high me-2",children:"High (15-16)"}),(0,u.jsx)("span",{className:"badge risk-extreme",children:"Extreme (20-25)"})]})]})]})},b=()=>(0,u.jsxs)(o.s,{children:[(0,u.jsx)(n.U,{xs:12,children:(0,u.jsx)(f,{onCellClick:(e,r)=>{console.log("Risk selected: Likelihood ".concat(e,", Consequence ").concat(r))}})}),(0,u.jsx)(n.U,{xs:12,className:"mt-4",children:(0,u.jsxs)(s.E,{children:[(0,u.jsx)(a.V,{children:(0,u.jsx)("strong",{children:"Risk Assessment Guidelines"})}),(0,u.jsxs)(t.W,{children:[(0,u.jsx)("h5",{children:"Likelihood Definitions"}),(0,u.jsxs)("ul",{children:[(0,u.jsxs)("li",{children:[(0,u.jsx)("strong",{children:"Rare (1)"}),": May occur only in exceptional circumstances (less than once per 10 years)"]}),(0,u.jsxs)("li",{children:[(0,u.jsx)("strong",{children:"Unlikely (2)"}),": Could occur at some time (once per 5-10 years)"]}),(0,u.jsxs)("li",{children:[(0,u.jsx)("strong",{children:"Possible (3)"}),": Might occur at some time (once per 1-5 years)"]}),(0,u.jsxs)("li",{children:[(0,u.jsx)("strong",{children:"Likely (4)"}),": Will probably occur (once per year)"]}),(0,u.jsxs)("li",{children:[(0,u.jsx)("strong",{children:"Almost Certain (5)"}),": Is expected to occur (multiple times per year)"]})]}),(0,u.jsx)("h5",{className:"mt-4",children:"Consequence Definitions"}),(0,u.jsxs)("ul",{children:[(0,u.jsxs)("li",{children:[(0,u.jsx)("strong",{children:"Insignificant (1)"}),": No injuries, low financial loss"]}),(0,u.jsxs)("li",{children:[(0,u.jsx)("strong",{children:"Minor (2)"}),": First aid treatment, medium financial loss"]}),(0,u.jsxs)("li",{children:[(0,u.jsx)("strong",{children:"Moderate (3)"}),": Medical treatment required, high financial loss"]}),(0,u.jsxs)("li",{children:[(0,u.jsx)("strong",{children:"Major (4)"}),": Extensive injuries, major financial loss"]}),(0,u.jsxs)("li",{children:[(0,u.jsx)("strong",{children:"Severe (5)"}),": Death or permanent disability, huge financial loss"]})]})]})]})})]})},67111:(e,r,l)=>{l.d(r,{$:()=>i});var o=l(3035),n=l(9950),s=l(11942),a=l.n(s),t=l(69344),c=l(3319),i=(0,n.forwardRef)((function(e,r){var l,s=e.children,a=e.className,c=e.color,i=(0,o.Tt)(e,["children","className","color"]);return n.createElement("th",(0,o.Cl)({className:(0,t.A)((l={},l["table-".concat(c)]=c,l),a)||void 0},i,{ref:r}),s)}));i.propTypes={children:a().node,className:a().string,color:c.TX},i.displayName="CTableHeaderCell"},71398:(e,r,l)=>{l.d(r,{U:()=>i});var o=l(3035),n=l(9950),s=l(11942),a=l.n(s),t=l(69344),c=["xxl","xl","lg","md","sm","xs"],i=(0,n.forwardRef)((function(e,r){var l=e.children,s=e.className,a=(0,o.Tt)(e,["children","className"]),i=[];return c.forEach((function(e){var r=a[e];delete a[e];var l="xs"===e?"":"-".concat(e);"number"!==typeof r&&"string"!==typeof r||i.push("col".concat(l,"-").concat(r)),"boolean"===typeof r&&i.push("col".concat(l)),r&&"object"===typeof r&&("number"!==typeof r.span&&"string"!==typeof r.span||i.push("col".concat(l,"-").concat(r.span)),"boolean"===typeof r.span&&i.push("col".concat(l)),"number"!==typeof r.order&&"string"!==typeof r.order||i.push("order".concat(l,"-").concat(r.order)),"number"===typeof r.offset&&i.push("offset".concat(l,"-").concat(r.offset)))})),n.createElement("div",(0,o.Cl)({className:(0,t.A)(i.length>0?i:"col",s)},a,{ref:r}),l)})),d=a().oneOfType([a().bool,a().number,a().string,a().oneOf(["auto"])]),p=a().oneOfType([d,a().shape({span:d,offset:a().oneOfType([a().number,a().string]),order:a().oneOfType([a().oneOf(["first","last"]),a().number,a().string])})]);i.propTypes={children:a().node,className:a().string,xs:p,sm:p,md:p,lg:p,xl:p,xxl:p},i.displayName="CCol"},98114:(e,r,l)=>{l.d(r,{W:()=>c});var o=l(3035),n=l(9950),s=l(11942),a=l.n(s),t=l(69344),c=(0,n.forwardRef)((function(e,r){var l=e.children,s=e.className,a=(0,o.Tt)(e,["children","className"]);return n.createElement("div",(0,o.Cl)({className:(0,t.A)("card-body",s)},a,{ref:r}),l)}));c.propTypes={children:a().node,className:a().string},c.displayName="CCardBody"}}]);