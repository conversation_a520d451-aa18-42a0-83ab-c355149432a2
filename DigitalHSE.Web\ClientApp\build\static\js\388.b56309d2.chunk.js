"use strict";(self.webpackChunkdigitalhse_web=self.webpackChunkdigitalhse_web||[]).push([[388],{77388:(e,s,i)=>{i.r(s),i.d(s,{default:()=>Se});var t=i(89379),n=i(9950),a=i(52684),c=i(71398),r=i(19124),l=i(38290),d=i(50025),o=i(41372),m=i(93961),u=i(92124),h=i(23793),x=i(76548),p=i(68852),y=i(85042),j=i(95304),g=i(61114),v=i(33652),f=i(14778),b=i(79522),N=i(3628),A=i(64771),k=i(67583),C=i(35642),w=i(39696),S=i(5617),P=i(1093),M=i(40565),R=i(30578),T=i(13019),D=i(71028),I=i(17831),E=i(45728),$=i(98114),q=i(15170),O=i(97098),z=i(64831),_=i(5356),B=i(72922),U=i(9134),X=i(40121),G=i(8236),H=i(67111),Q=i(63898),W=i(4902),F=i(23561),J=i(87905),L=i(97572),V=i(8134),K=i(2977),Y=i(6842),Z=i(54732),ee=i(63943),se=i(62293),ie=i(38886),te=i(94926),ne=i(78131),ae=i(3526),ce=i(38806),re=i(97199),le=i(84746),de=i(79367),oe=i(22788),me=i(17011),ue=i(37656),he=i(7632),xe=i(26577),pe=i(81949),ye=i(67818),je=i(28429),ge=i(45753),ve=i(82144),fe=i(64787),be=i(58422),Ne=i(38191),Ae=i(32113),ke=i(85923),Ce=i(79894),we=i(44414);const Se=()=>{const{t:e,i18n:s}=(0,ye.Bd)(),i=(0,je.Zp)(),[Se,Pe]=(0,n.useState)(!1),[Me,Re]=(0,n.useState)([]),[Te,De]=(0,n.useState)(null),[Ie,Ee]=(0,n.useState)(1),[$e,qe]=(0,n.useState)(20),[Oe,ze]=(0,n.useState)(0),[_e,Be]=(0,n.useState)("incidentDateTime"),[Ue,Xe]=(0,n.useState)("desc"),[Ge,He]=(0,n.useState)("list"),[Qe,We]=(0,n.useState)(!1),[Fe,Je]=(0,n.useState)([]),[Le,Ve]=(0,n.useState)(3e4),[Ke,Ye]=(0,n.useState)(new Date),[Ze,es]=(0,n.useState)(!1),[ss,is]=(0,n.useState)("all"),[ts,ns]=(0,n.useState)({searchTerm:"",category:"",type:"",severity:"",urgency:"",status:"",investigationStatus:"",dateFrom:"",dateTo:"",department:"",building:"",location:"",personAffectedType:"",reporterType:"",isStudentIncident:"",isAnonymous:"",requiresBPJSReporting:"",requiresMinistryReporting:"",isOverdue:"",isRegulatoryDeadlinePending:"",requiresMedicalAttention:"",parentNotified:"",academicYear:"",term:""}),[as,cs]=(0,n.useState)({categories:[],types:[],severities:[],urgencies:[],personTypes:[],statuses:[],investigationStatuses:[]}),rs=Math.ceil(Oe/$e);(0,n.useEffect)((()=>{(async()=>{try{const[s,i,t,n,a]=await Promise.all([Ce.X.get("/api/incident/categories"),Ce.X.get("/api/incident/types"),Ce.X.get("/api/incident/severities"),Ce.X.get("/api/incident/urgencies"),Ce.X.get("/api/incident/person-types")]),c=e=>e.success&&Array.isArray(e.data)||Array.isArray(e.data)?e.data:Array.isArray(e)?e:[];cs({categories:c(s),types:c(i),severities:c(t),urgencies:c(n),personTypes:c(a),statuses:[{value:"Reported",display:e("incident.status.reported")},{value:"Acknowledged",display:e("incident.status.acknowledged")},{value:"InProgress",display:e("incident.status.inProgress")},{value:"Resolved",display:e("incident.status.resolved")},{value:"Closed",display:e("incident.status.closed")}],investigationStatuses:[{value:"NotStarted",display:e("incident.investigation.notStarted")},{value:"InProgress",display:e("incident.investigation.inProgress")},{value:"Completed",display:e("incident.investigation.completed")}]})}catch(s){console.error("Failed to load dropdown data:",s),cs({categories:[],types:[],severities:[],urgencies:[],personTypes:[],statuses:[{value:"Reported",display:e("incident.status.reported")},{value:"Acknowledged",display:e("incident.status.acknowledged")},{value:"InProgress",display:e("incident.status.inProgress")},{value:"Resolved",display:e("incident.status.resolved")},{value:"Closed",display:e("incident.status.closed")}],investigationStatuses:[{value:"NotStarted",display:e("incident.investigation.notStarted")},{value:"InProgress",display:e("incident.investigation.inProgress")},{value:"Completed",display:e("incident.investigation.completed")}]})}})()}),[e]);const ls=(0,n.useCallback)((()=>{const e=new URLSearchParams;return e.append("pageNumber",Ie.toString()),e.append("pageSize",$e.toString()),e.append("sortBy",_e),e.append("sortDirection",Ue),Object.entries(ts).forEach((s=>{let[i,t]=s;t&&e.append(i,t)})),e.toString()}),[Ie,$e,_e,Ue,ts]),ds=(0,n.useCallback)((async()=>{Pe(!0);try{const e=ls(),s=await Ce.X.get("/api/incident?".concat(e));s.success&&(Re(s.data.items||[]),ze(s.data.totalCount||0),Ye(new Date))}catch(s){console.error("Failed to load incidents:",s),ke.oR.error(e("incident.errors.loadingIncidents"))}finally{Pe(!1)}}),[ls,e]),os=(0,n.useCallback)((async()=>{try{const e=await Ce.X.get("/api/incident/statistics");e.success&&De(e.data)}catch(e){console.error("Failed to load statistics:",e)}}),[]);(0,n.useEffect)((()=>{ds(),os()}),[ds,os]),(0,n.useEffect)((()=>{if(Le){const e=setInterval((()=>{ds(),os()}),Le);return()=>clearInterval(e)}}),[Le,ds,os]),(0,n.useEffect)((()=>{const e=new Date;let s="",i="";switch(ss){case"today":s=(0,ge.GP)(e,"yyyy-MM-dd"),i=(0,ge.GP)(e,"yyyy-MM-dd");break;case"yesterday":const t=new Date(e);t.setDate(t.getDate()-1),s=(0,ge.GP)(t,"yyyy-MM-dd"),i=(0,ge.GP)(t,"yyyy-MM-dd");break;case"thisWeek":s=(0,ge.GP)((0,ve.k)(e),"yyyy-MM-dd"),i=(0,ge.GP)((0,fe.$)(e),"yyyy-MM-dd");break;case"thisMonth":s=(0,ge.GP)((0,be.w)(e),"yyyy-MM-dd"),i=(0,ge.GP)((0,Ne.p)(e),"yyyy-MM-dd");break;case"last7Days":const n=new Date(e);n.setDate(n.getDate()-7),s=(0,ge.GP)(n,"yyyy-MM-dd"),i=(0,ge.GP)(e,"yyyy-MM-dd");break;case"last30Days":const a=new Date(e);a.setDate(a.getDate()-30),s=(0,ge.GP)(a,"yyyy-MM-dd"),i=(0,ge.GP)(e,"yyyy-MM-dd")}ns((e=>(0,t.A)((0,t.A)({},e),{},{dateFrom:s,dateTo:i})))}),[ss]);const ms=(e,s)=>{ns((i=>(0,t.A)((0,t.A)({},i),{},{[e]:s}))),Ee(1)},us=()=>{ns({searchTerm:"",category:"",type:"",severity:"",urgency:"",status:"",investigationStatus:"",dateFrom:"",dateTo:"",department:"",building:"",location:"",personAffectedType:"",reporterType:"",isStudentIncident:"",isAnonymous:"",requiresBPJSReporting:"",requiresMinistryReporting:"",isOverdue:"",isRegulatoryDeadlinePending:"",requiresMedicalAttention:"",parentNotified:"",academicYear:"",term:""}),is("all"),Ee(1)},hs=e=>{_e===e?Xe((e=>"asc"===e?"desc":"asc")):(Be(e),Xe("desc")),Ee(1)},xs=async s=>{es(!0);try{const i=ls(),t=await Ce.X.get("/api/incident/export?format=".concat(s,"&").concat(i));if(t.success){const i=window.URL.createObjectURL(new Blob([t.data])),n=document.createElement("a");n.href=i,n.setAttribute("download","incidents_".concat(s,"_").concat(s(new Date,"yyyyMMdd_HHmmss"),".").concat("excel"===s?"xlsx":"csv")),document.body.appendChild(n),n.click(),n.remove(),ke.oR.success(e("incident.export.success"))}else ke.oR.error(t.message||e("incident.export.error"))}catch(i){console.error("Export failed:",i),ke.oR.error(e("incident.export.error"))}finally{es(!1)}},ps=e=>{switch(e){case 5:return"danger";case 4:return"warning";case 3:return"info";case 2:return"primary";default:return"secondary"}},ys=e=>{switch(e){case"Closed":return"success";case"Resolved":return"info";case"InProgress":return"primary";case"Acknowledged":return"warning";default:return"secondary"}},js=e=>{switch(e){case"Completed":return"success";case"InProgress":return"primary";default:return"secondary"}},gs=(0,n.useMemo)((()=>Object.values(ts).filter((e=>""!==e)).length),[ts]);return(0,we.jsxs)(A.T,{fluid:!0,children:[(0,we.jsx)(a.s,{className:"mb-4",children:(0,we.jsx)(c.U,{children:(0,we.jsxs)("div",{className:"d-flex justify-content-between align-items-center",children:[(0,we.jsxs)("div",{children:[(0,we.jsx)("h2",{children:e("incident.list.title")}),(0,we.jsxs)("p",{className:"text-muted mb-0",children:[e("incident.list.subtitle",{count:Oe}),Ke&&(0,we.jsxs)("span",{className:"ms-2",children:["\u2022 ",e("incident.list.lastUpdate"),": ",(0,ge.GP)(Ke,"HH:mm:ss")]})]})]}),(0,we.jsxs)("div",{className:"d-flex gap-2",children:[(0,we.jsxs)(g.Q,{color:"primary",onClick:()=>i("/incidents/new"),children:[(0,we.jsx)(pe.Ay,{icon:ae.x,className:"me-2"}),e("incident.actions.report")]}),(0,we.jsxs)(k.j,{children:[(0,we.jsxs)(C.V,{color:"secondary",disabled:Ze,children:[Ze?(0,we.jsx)(w.J,{size:"sm"}):(0,we.jsx)(pe.Ay,{icon:ce.s,className:"me-2"}),e("incident.actions.export")]}),(0,we.jsxs)(S.Q,{children:[(0,we.jsxs)(P.k,{onClick:()=>xs("excel"),children:[(0,we.jsx)(pe.Ay,{icon:ce.s,className:"me-2"}),e("incident.export.excel")]}),(0,we.jsxs)(P.k,{onClick:()=>xs("csv"),children:[(0,we.jsx)(pe.Ay,{icon:ce.s,className:"me-2"}),e("incident.export.csv")]}),(0,we.jsx)(M.A,{}),(0,we.jsxs)(P.k,{onClick:()=>window.print(),children:[(0,we.jsx)(pe.Ay,{icon:re.C,className:"me-2"}),e("incident.export.print")]})]})]})]})]})})}),Te?(0,we.jsxs)(a.s,{className:"mb-4",children:[(0,we.jsx)(c.U,{xs:6,md:3,children:(0,we.jsx)(r.N,{className:"mb-3",color:"primary",icon:(0,we.jsx)(pe.Ay,{icon:L.p,height:24}),title:e("incident.stats.total"),value:Te.total,footer:(0,we.jsxs)("span",{className:"text-medium-emphasis",children:[Te.todayCount," ",e("incident.stats.today")]})})}),(0,we.jsx)(c.U,{xs:6,md:3,children:(0,we.jsx)(r.N,{className:"mb-3",color:"warning",icon:(0,we.jsx)(pe.Ay,{icon:V.V,height:24}),title:e("incident.stats.open"),value:Te.open,footer:(0,we.jsxs)("span",{className:"text-medium-emphasis",children:[Te.investigating," ",e("incident.stats.investigating")]})})}),(0,we.jsx)(c.U,{xs:6,md:3,children:(0,we.jsx)(r.N,{className:"mb-3",color:"danger",icon:(0,we.jsx)(pe.Ay,{icon:L.p,height:24}),title:e("incident.stats.overdue"),value:Te.overdue,footer:(0,we.jsxs)("span",{className:"text-medium-emphasis",children:[Te.highSeverity," ",e("incident.stats.highSeverity")]})})}),(0,we.jsx)(c.U,{xs:6,md:3,children:(0,we.jsx)(r.N,{className:"mb-3",color:"success",icon:(0,we.jsx)(pe.Ay,{icon:K.j,height:24}),title:e("incident.stats.closed"),value:Te.closed,footer:(0,we.jsx)(l.f,{thin:!0,className:"mt-3",color:"success",value:Te.complianceRate||0,children:(0,we.jsxs)(d.E,{value:Te.complianceRate,children:[Te.complianceRate,"% ",e("incident.stats.compliant")]})})})})]}):null,(0,we.jsxs)(R.E,{children:[(0,we.jsx)(T.V,{children:(0,we.jsxs)(D.b,{variant:"tabs",role:"tablist",children:[(0,we.jsx)(I.g,{children:(0,we.jsxs)(E.H,{active:"list"===Ge,onClick:()=>He("list"),children:[(0,we.jsx)(pe.Ay,{icon:le.R,className:"me-2"}),e("incident.tabs.list")]})}),(0,we.jsx)(I.g,{children:(0,we.jsxs)(E.H,{active:"analytics"===Ge,onClick:()=>He("analytics"),children:[(0,we.jsx)(pe.Ay,{icon:de.N,className:"me-2"}),e("incident.tabs.analytics")]})})]})}),(0,we.jsx)($.W,{children:(0,we.jsxs)(q.e,{children:[(0,we.jsxs)(O.x,{visible:"list"===Ge,children:[(0,we.jsxs)("div",{className:"d-flex justify-content-between align-items-center mb-3",children:[(0,we.jsxs)("div",{className:"d-flex gap-2 align-items-center",children:[(0,we.jsxs)(z.B,{style:{width:"300px"},children:[(0,we.jsx)(_.s,{children:(0,we.jsx)(pe.Ay,{icon:Y.B})}),(0,we.jsx)(p.O,{placeholder:e("incident.search.placeholder"),value:ts.searchTerm,onChange:e=>ms("searchTerm",e.target.value)})]}),(0,we.jsxs)(g.Q,{color:"primary",variant:"outline",onClick:()=>We(!0),children:[(0,we.jsx)(pe.Ay,{icon:oe.R,className:"me-2"}),e("incident.filters.button"),gs>0&&(0,we.jsx)(N.$,{color:"danger",className:"ms-2",shape:"rounded-pill",children:gs})]}),(0,we.jsx)(g.Q,{color:"secondary",variant:"outline",onClick:()=>ds(),children:(0,we.jsx)(pe.Ay,{icon:me.Q})})]}),(0,we.jsxs)("div",{className:"d-flex gap-2 align-items-center",children:[(0,we.jsxs)(y.M,{style:{width:"auto"},value:$e,onChange:e=>{qe(Number(e.target.value)),Ee(1)},children:[(0,we.jsxs)("option",{value:"10",children:["10 ",e("common.perPage")]}),(0,we.jsxs)("option",{value:"20",children:["20 ",e("common.perPage")]}),(0,we.jsxs)("option",{value:"50",children:["50 ",e("common.perPage")]}),(0,we.jsxs)("option",{value:"100",children:["100 ",e("common.perPage")]})]}),(0,we.jsxs)(y.M,{style:{width:"auto"},value:(null===Le||void 0===Le?void 0:Le.toString())||"manual",onChange:e=>{const s=e.target.value;Ve("manual"===s?null:Number(s))},children:[(0,we.jsx)("option",{value:"manual",children:e("incident.refresh.manual")}),(0,we.jsx)("option",{value:"30000",children:e("incident.refresh.30s")}),(0,we.jsx)("option",{value:"60000",children:e("incident.refresh.1m")}),(0,we.jsx)("option",{value:"300000",children:e("incident.refresh.5m")})]})]})]}),gs>0&&(0,we.jsx)(B.M,{color:"info",className:"mb-3",children:(0,we.jsxs)("div",{className:"d-flex justify-content-between align-items-center",children:[(0,we.jsxs)("div",{children:[(0,we.jsx)("strong",{children:e("incident.filters.active",{count:gs})}),(0,we.jsx)("div",{className:"mt-1",children:Object.entries(ts).map((s=>{let[i,t]=s;return t?(0,we.jsxs)(N.$,{color:"info",className:"me-2 mb-1",children:[e("incident.filters.".concat(i)),": ",t]},i):null}))})]}),(0,we.jsx)(g.Q,{color:"info",variant:"ghost",size:"sm",onClick:us,children:e("incident.filters.clearAll")})]})}),Se?(0,we.jsxs)("div",{className:"text-center py-5",children:[(0,we.jsx)(w.J,{color:"primary"}),(0,we.jsx)("div",{className:"mt-2",children:e("common.loading")})]}):0===Me.length?(0,we.jsxs)(U.k,{color:"info",children:[(0,we.jsx)(pe.Ay,{icon:te.v,className:"me-2"}),e(gs>0?"incident.list.noResultsWithFilters":"incident.list.noIncidents")]}):(0,we.jsxs)(we.Fragment,{children:[(0,we.jsx)("div",{className:"table-responsive",children:(0,we.jsxs)(X._,{hover:!0,children:[(0,we.jsx)(G.w,{children:(0,we.jsxs)(v.Y,{children:[(0,we.jsx)(H.$,{style:{width:"40px"},children:(0,we.jsx)(j.C,{checked:Fe.length===Me.length&&Me.length>0,onChange:e=>{return s=e.target.checked,void Je(s?Me.map((e=>e.id)):[]);var s}})}),(0,we.jsxs)(H.$,{style:{cursor:"pointer",width:"120px"},onClick:()=>hs("incidentNumber"),children:[e("incident.fields.number"),"incidentNumber"===_e&&(0,we.jsx)(pe.Ay,{icon:"asc"===Ue?ue.w:he.m,size:"sm",className:"ms-1"})]}),(0,we.jsxs)(H.$,{style:{cursor:"pointer"},onClick:()=>hs("title"),children:[e("incident.fields.title"),"title"===_e&&(0,we.jsx)(pe.Ay,{icon:"asc"===Ue?ue.w:he.m,size:"sm",className:"ms-1"})]}),(0,we.jsxs)(H.$,{style:{cursor:"pointer",width:"100px"},onClick:()=>hs("severity"),children:[e("incident.fields.severity"),"severity"===_e&&(0,we.jsx)(pe.Ay,{icon:"asc"===Ue?ue.w:he.m,size:"sm",className:"ms-1"})]}),(0,we.jsx)(H.$,{style:{width:"150px"},children:e("incident.fields.status")}),(0,we.jsx)(H.$,{style:{width:"140px"},children:e("incident.fields.compliance")}),(0,we.jsx)(H.$,{style:{width:"150px"},children:e("incident.fields.personAffected")}),(0,we.jsx)(H.$,{style:{width:"120px"},children:e("incident.fields.reportedBy")}),(0,we.jsx)(H.$,{style:{width:"120px"},children:e("common.actions")})]})}),(0,we.jsx)(Q.C,{children:Me.map((s=>(0,we.jsxs)(v.Y,{className:s.isUrgent?"table-danger":"",children:[(0,we.jsx)(f.c,{children:(0,we.jsx)(j.C,{checked:Fe.includes(s.id),onChange:e=>{return i=s.id,t=e.target.checked,void Je(t?e=>[...e,i]:e=>e.filter((e=>e!==i)));var i,t}})}),(0,we.jsxs)(f.c,{children:[(0,we.jsxs)("div",{className:"fw-semibold",children:[s.incidentNumber,s.isAnonymous&&(0,we.jsx)(b.j,{content:e("incident.anonymous.tracking",{code:s.anonymousTrackingCode}),children:(0,we.jsx)(N.$,{color:"secondary",className:"ms-1",children:"A"})})]}),(0,we.jsx)("small",{className:"text-muted",children:(0,ge.GP)((0,Ae.H)(s.incidentDateTime),"dd/MM/yyyy HH:mm")})]}),(0,we.jsxs)(f.c,{children:[(0,we.jsx)("div",{className:"text-truncate",style:{maxWidth:"300px"},children:(0,we.jsx)(b.j,{content:s.description,children:(0,we.jsx)("span",{className:"fw-medium",children:s.title})})}),(0,we.jsxs)("div",{className:"d-flex gap-1 mt-1",children:[(0,we.jsxs)(N.$,{color:"light",textColor:"dark",children:[(0,we.jsx)(pe.Ay,{icon:ee.r,size:"sm",className:"me-1"}),s.building," - ",s.location]}),s.isStudentIncident&&(0,we.jsxs)(N.$,{color:"info",children:[(0,we.jsx)(pe.Ay,{icon:se.o,size:"sm",className:"me-1"}),e("incident.student")]})]})]}),(0,we.jsx)(f.c,{children:(0,we.jsx)(N.$,{color:ps(s.severity),children:s.severityDisplay})}),(0,we.jsxs)(f.c,{children:[(0,we.jsx)("div",{children:(0,we.jsx)(N.$,{color:ys(s.status),children:s.statusDisplay})}),s.investigationStatus&&(0,we.jsx)("div",{className:"mt-1",children:(0,we.jsxs)(N.$,{color:js(s.investigationStatus),textColor:"white",children:[(0,we.jsx)(pe.Ay,{icon:ie.S,size:"sm",className:"me-1"}),s.investigationStatusDisplay]})})]}),(0,we.jsx)(f.c,{children:(0,we.jsxs)("div",{className:"d-flex flex-column gap-1",children:[s.requiresBPJSReporting&&(0,we.jsx)(b.j,{content:s.bpjsReferenceNumber||e("incident.compliance.bpjsPending"),children:(0,we.jsxs)(N.$,{color:s.bpjsReferenceNumber?"success":"warning",children:["BPJS ",s.bpjsReferenceNumber?"\u2713":"!"]})}),s.requiresMinistryReporting&&(0,we.jsx)(b.j,{content:s.ministryReferenceNumber||e("incident.compliance.ministryPending"),children:(0,we.jsxs)(N.$,{color:s.ministryReferenceNumber?"success":"warning",children:["Ministry ",s.ministryReferenceNumber?"\u2713":"!"]})}),s.isRegulatoryOverdue&&(0,we.jsxs)(N.$,{color:"danger",children:[(0,we.jsx)(pe.Ay,{icon:V.V,size:"sm",className:"me-1"}),e("incident.compliance.overdue")]})]})}),(0,we.jsxs)(f.c,{children:[(0,we.jsxs)("div",{children:[s.personAffectedName||"-",s.personAffectedClass&&(0,we.jsx)("div",{className:"small text-muted",children:s.personAffectedClass})]}),s.parentNotified&&(0,we.jsxs)(N.$,{color:"success",className:"mt-1",children:[(0,we.jsx)(pe.Ay,{icon:K.j,size:"sm",className:"me-1"}),e("incident.parent.notified")]})]}),(0,we.jsx)(f.c,{children:(0,we.jsxs)("div",{className:"small",children:[(0,we.jsx)("div",{children:s.reportedBy}),(0,we.jsx)("div",{className:"text-muted",children:s.reporterTypeDisplay})]})}),(0,we.jsx)(f.c,{children:(0,we.jsxs)("div",{className:"d-flex gap-1",children:[(0,we.jsx)(b.j,{content:e("incident.actions.view"),children:(0,we.jsx)(g.Q,{color:"primary",size:"sm",variant:"ghost",onClick:()=>i("/incidents/".concat(s.id)),children:(0,we.jsx)(pe.Ay,{icon:te.v})})}),(0,we.jsx)(b.j,{content:e("incident.actions.edit"),children:(0,we.jsx)(g.Q,{color:"info",size:"sm",variant:"ghost",onClick:()=>i("/incidents/".concat(s.id,"/edit")),children:(0,we.jsx)(pe.Ay,{icon:ne.K})})}),"NotStarted"===s.investigationStatus&&(0,we.jsx)(b.j,{content:e("incident.actions.investigate"),children:(0,we.jsx)(g.Q,{color:"warning",size:"sm",variant:"ghost",onClick:()=>i("/incidents/".concat(s.id,"/investigate")),children:(0,we.jsx)(pe.Ay,{icon:ie.S})})})]})})]},s.id)))})]})}),rs>1&&(0,we.jsxs)("div",{className:"d-flex justify-content-between align-items-center mt-3",children:[(0,we.jsx)("div",{className:"text-muted",children:e("common.showing",{from:(Ie-1)*$e+1,to:Math.min(Ie*$e,Oe),total:Oe})}),(0,we.jsxs)(W._,{children:[(0,we.jsx)(F.X,{disabled:1===Ie,onClick:()=>Ee(1),children:e("common.first")}),(0,we.jsx)(F.X,{disabled:1===Ie,onClick:()=>Ee((e=>Math.max(1,e-1))),children:e("common.previous")}),[...Array(Math.min(5,rs))].map(((e,s)=>{const i=Ie-2+s;return i<1||i>rs?null:(0,we.jsx)(F.X,{active:i===Ie,onClick:()=>Ee(i),children:i},i)})),(0,we.jsx)(F.X,{disabled:Ie===rs,onClick:()=>Ee((e=>Math.min(rs,e+1))),children:e("common.next")}),(0,we.jsx)(F.X,{disabled:Ie===rs,onClick:()=>Ee(rs),children:e("common.last")})]})]})]}),Fe.length>0&&(0,we.jsx)("div",{className:"position-fixed bottom-0 start-50 translate-middle-x mb-3",children:(0,we.jsx)(R.E,{className:"shadow-lg",children:(0,we.jsx)($.W,{className:"py-2",children:(0,we.jsxs)("div",{className:"d-flex align-items-center gap-3",children:[(0,we.jsx)("span",{className:"text-muted",children:e("incident.bulk.selected",{count:Fe.length})}),(0,we.jsxs)(J.$,{children:[(0,we.jsxs)(g.Q,{color:"primary",size:"sm",children:[(0,we.jsx)(pe.Ay,{icon:K.j,className:"me-1"}),e("incident.bulk.assign")]}),(0,we.jsxs)(g.Q,{color:"warning",size:"sm",children:[(0,we.jsx)(pe.Ay,{icon:ie.S,className:"me-1"}),e("incident.bulk.investigate")]}),(0,we.jsxs)(g.Q,{color:"danger",size:"sm",children:[(0,we.jsx)(pe.Ay,{icon:xe.a,className:"me-1"}),e("incident.bulk.close")]})]})]})})})})]}),(0,we.jsx)(O.x,{visible:"analytics"===Ge,children:Te&&(0,we.jsxs)(a.s,{children:[(0,we.jsx)(c.U,{md:6,children:(0,we.jsxs)(R.E,{className:"mb-4",children:[(0,we.jsx)(T.V,{children:e("incident.analytics.byCategory")}),(0,we.jsx)($.W,{children:(0,we.jsx)(U.k,{color:"info",children:e("incident.analytics.chartPlaceholder")})})]})}),(0,we.jsx)(c.U,{md:6,children:(0,we.jsxs)(R.E,{className:"mb-4",children:[(0,we.jsx)(T.V,{children:e("incident.analytics.byLocation")}),(0,we.jsx)($.W,{children:(0,we.jsx)(U.k,{color:"info",children:e("incident.analytics.chartPlaceholder")})})]})}),(0,we.jsx)(c.U,{md:12,children:(0,we.jsxs)(R.E,{children:[(0,we.jsx)(T.V,{children:e("incident.analytics.trends")}),(0,we.jsx)($.W,{children:(0,we.jsx)(U.k,{color:"info",children:e("incident.analytics.chartPlaceholder")})})]})})]})})]})})]}),(0,we.jsxs)(o.F,{placement:"end",visible:Qe,onHide:()=>We(!1),className:"w-auto",children:[(0,we.jsxs)(m.C,{children:[(0,we.jsx)(u.z,{children:e("incident.filters.title")}),(0,we.jsx)(h.E,{className:"text-reset",onClick:()=>We(!1)})]}),(0,we.jsx)(x.X,{children:(0,we.jsxs)("div",{className:"d-grid gap-3",style:{minWidth:"350px"},children:[(0,we.jsxs)("div",{children:[(0,we.jsx)("label",{className:"form-label",children:e("incident.filters.search")}),(0,we.jsx)(p.O,{type:"text",placeholder:e("incident.filters.searchPlaceholder"),value:ts.searchTerm,onChange:e=>ms("searchTerm",e.target.value)})]}),(0,we.jsxs)("div",{children:[(0,we.jsx)("label",{className:"form-label",children:e("incident.filters.dateRange")}),(0,we.jsxs)(y.M,{value:ss,onChange:e=>is(e.target.value),children:[(0,we.jsx)("option",{value:"all",children:e("incident.filters.allTime")}),(0,we.jsx)("option",{value:"today",children:e("incident.filters.today")}),(0,we.jsx)("option",{value:"yesterday",children:e("incident.filters.yesterday")}),(0,we.jsx)("option",{value:"thisWeek",children:e("incident.filters.thisWeek")}),(0,we.jsx)("option",{value:"thisMonth",children:e("incident.filters.thisMonth")}),(0,we.jsx)("option",{value:"last7Days",children:e("incident.filters.last7Days")}),(0,we.jsx)("option",{value:"last30Days",children:e("incident.filters.last30Days")})]})]}),(0,we.jsxs)("div",{children:[(0,we.jsx)("label",{className:"form-label",children:e("incident.filters.category")}),(0,we.jsxs)(y.M,{value:ts.category,onChange:e=>ms("category",e.target.value),children:[(0,we.jsx)("option",{value:"",children:e("common.all")}),Array.isArray(as.categories)&&as.categories.map((e=>(0,we.jsx)("option",{value:e.value,children:e.display},e.value)))]})]}),(0,we.jsxs)("div",{children:[(0,we.jsx)("label",{className:"form-label",children:e("incident.filters.type")}),(0,we.jsxs)(y.M,{value:ts.type,onChange:e=>ms("type",e.target.value),children:[(0,we.jsx)("option",{value:"",children:e("common.all")}),Array.isArray(as.types)&&as.types.map((e=>(0,we.jsx)("option",{value:e.value,children:e.display},e.value)))]})]}),(0,we.jsxs)("div",{children:[(0,we.jsx)("label",{className:"form-label",children:e("incident.filters.severity")}),(0,we.jsxs)(y.M,{value:ts.severity,onChange:e=>ms("severity",e.target.value),children:[(0,we.jsx)("option",{value:"",children:e("common.all")}),Array.isArray(as.severities)&&as.severities.map((e=>(0,we.jsx)("option",{value:e.value,children:e.display},e.value)))]})]}),(0,we.jsxs)("div",{children:[(0,we.jsx)("label",{className:"form-label",children:e("incident.filters.status")}),(0,we.jsxs)(y.M,{value:ts.status,onChange:e=>ms("status",e.target.value),children:[(0,we.jsx)("option",{value:"",children:e("common.all")}),Array.isArray(as.statuses)&&as.statuses.map((e=>(0,we.jsx)("option",{value:e.value,children:e.display},e.value)))]})]}),(0,we.jsxs)("div",{children:[(0,we.jsx)("label",{className:"form-label",children:e("incident.filters.investigationStatus")}),(0,we.jsxs)(y.M,{value:ts.investigationStatus,onChange:e=>ms("investigationStatus",e.target.value),children:[(0,we.jsx)("option",{value:"",children:e("common.all")}),Array.isArray(as.investigationStatuses)&&as.investigationStatuses.map((e=>(0,we.jsx)("option",{value:e.value,children:e.display},e.value)))]})]}),(0,we.jsxs)("div",{children:[(0,we.jsx)("label",{className:"form-label",children:e("incident.filters.building")}),(0,we.jsx)(p.O,{type:"text",value:ts.building,onChange:e=>ms("building",e.target.value)})]}),(0,we.jsxs)("div",{children:[(0,we.jsx)("label",{className:"form-label",children:e("incident.filters.personAffectedType")}),(0,we.jsxs)(y.M,{value:ts.personAffectedType,onChange:e=>ms("personAffectedType",e.target.value),children:[(0,we.jsx)("option",{value:"",children:e("common.all")}),Array.isArray(as.personTypes)&&as.personTypes.map((e=>(0,we.jsx)("option",{value:e.value,children:e.display},e.value)))]})]}),(0,we.jsxs)("div",{children:[(0,we.jsx)("label",{className:"form-label",children:e("incident.filters.special")}),(0,we.jsx)(j.C,{id:"isStudentIncident",label:e("incident.filters.studentIncidents"),checked:"true"===ts.isStudentIncident,onChange:e=>ms("isStudentIncident",e.target.checked?"true":"")}),(0,we.jsx)(j.C,{id:"isOverdue",label:e("incident.filters.overdueOnly"),checked:"true"===ts.isOverdue,onChange:e=>ms("isOverdue",e.target.checked?"true":"")}),(0,we.jsx)(j.C,{id:"requiresBPJSReporting",label:e("incident.filters.requiresBPJS"),checked:"true"===ts.requiresBPJSReporting,onChange:e=>ms("requiresBPJSReporting",e.target.checked?"true":"")}),(0,we.jsx)(j.C,{id:"requiresMedicalAttention",label:e("incident.filters.requiresMedical"),checked:"true"===ts.requiresMedicalAttention,onChange:e=>ms("requiresMedicalAttention",e.target.checked?"true":"")})]}),(0,we.jsxs)("div",{className:"d-grid gap-2",children:[(0,we.jsxs)(g.Q,{color:"primary",onClick:()=>ds(),children:[(0,we.jsx)(pe.Ay,{icon:Y.B,className:"me-2"}),e("incident.filters.apply")]}),(0,we.jsxs)(g.Q,{color:"secondary",variant:"outline",onClick:us,children:[(0,we.jsx)(pe.Ay,{icon:Z.X,className:"me-2"}),e("incident.filters.clear")]})]})]})})]})]})}},79894:(e,s,i)=>{i.d(s,{X:()=>n});var t=i(82932);const n={async get(e){try{return{success:!0,data:(await t.Ay.get(e)).data}}catch(s){return console.error("API GET Error:",s),{success:!1,data:null,message:"Request failed"}}},async post(e,s,i){try{return{success:!0,data:(await t.Ay.post(e,s,i)).data}}catch(n){return console.error("API POST Error:",n),{success:!1,data:null,message:"Request failed"}}},async put(e,s){try{return{success:!0,data:(await t.Ay.put(e,s)).data}}catch(i){return console.error("API PUT Error:",i),{success:!1,data:null,message:"Request failed"}}},async delete(e){try{return{success:!0,data:(await t.Ay.delete(e)).data}}catch(s){return console.error("API DELETE Error:",s),{success:!1,data:null,message:"Request failed"}}}}},82932:(e,s,i)=>{i.d(s,{Ay:()=>c,n9:()=>r});var t=i(26910);const n={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_API_URL||"/api",a=t.A.create({baseURL:n,headers:{"Content-Type":"application/json"}});a.interceptors.request.use((e=>{const s=localStorage.getItem("token");return s&&(e.headers.Authorization="Bearer ".concat(s)),e})),a.interceptors.response.use((e=>e),(e=>{var s;return 401===(null===(s=e.response)||void 0===s?void 0:s.status)&&(localStorage.removeItem("token"),window.location.href="/login"),Promise.reject(e)}));const c=a,r={getDashboardStats:()=>a.get("/dashboard/stats"),getIncidents:e=>a.get("/incidents",{params:e}),createIncident:e=>a.post("/incidents",e),getIncident:e=>a.get("/incidents/".concat(e)),updateIncident:(e,s)=>a.put("/incidents/".concat(e),s),deleteIncident:e=>a.delete("/incidents/".concat(e)),getRiskMatrix:()=>a.get("/risks/matrix"),getRiskAssessments:e=>a.get("/risks/assessments",{params:e}),createRiskAssessment:e=>a.post("/risks/assessments",e),getRiskAssessment:e=>a.get("/risks/assessments/".concat(e)),updateRiskAssessment:(e,s)=>a.put("/risks/assessments/".concat(e),s),getPermits:e=>a.get("/permits",{params:e}),getActivePermits:()=>a.get("/permits/active"),createPermit:e=>a.post("/permits",e),getPermit:e=>a.get("/permits/".concat(e)),updatePermit:(e,s)=>a.put("/permits/".concat(e),s),approvePermit:e=>a.post("/permits/".concat(e,"/approve")),closePermit:e=>a.post("/permits/".concat(e,"/close")),getTrainingRecords:e=>a.get("/training",{params:e}),getTrainingCompliance:()=>a.get("/training/compliance"),createTrainingRecord:e=>a.post("/training",e),getDocuments:e=>a.get("/documents",{params:e}),uploadDocument:e=>a.post("/documents/upload",e,{headers:{"Content-Type":"multipart/form-data"}}),downloadDocument:e=>a.get("/documents/".concat(e,"/download"),{responseType:"blob"}),getAnalytics:e=>a.get("/analytics",{params:e}),getIncidentTrends:()=>a.get("/analytics/incident-trends"),getRiskHeatmap:()=>a.get("/analytics/risk-heatmap"),getComplianceMetrics:()=>a.get("/analytics/compliance-metrics")}}}]);