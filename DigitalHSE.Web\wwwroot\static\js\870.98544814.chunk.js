"use strict";(self.webpackChunkdigitalhse_web=self.webpackChunkdigitalhse_web||[]).push([[870],{9134:(e,t,s)=>{s.d(t,{k:()=>p});var a=s(3035),n=s(9950),r=s(11942),i=s.n(r),c=s(69344),o=s(23793),l=s(49115),d=s(3319),u=s(92729),p=(0,n.forwardRef)((function(e,t){var s=e.children,r=e.className,i=e.color,d=void 0===i?"primary":i,p=e.dismissible,m=e.variant,g=e.visible,h=void 0===g||g,y=e.onClose,f=(0,a.Tt)(e,["children","className","color","dismissible","variant","visible","onClose"]),b=(0,n.useRef)(null),v=(0,l.E2)(t,b),A=(0,n.useState)(h),E=A[0],T=A[1];return(0,n.useEffect)((function(){T(h)}),[h]),n.createElement(u.Ay,{in:E,mountOnEnter:!0,nodeRef:b,onExit:y,timeout:150,unmountOnExit:!0},(function(e){return n.createElement("div",(0,a.Cl)({className:(0,c.A)("alert","solid"===m?"bg-".concat(d," text-white"):"alert-".concat(d),{"alert-dismissible fade":p,show:"entered"===e},r),role:"alert"},f,{ref:v}),s,p&&n.createElement(o.E,{onClick:function(){return T(!1)}}))}))}));p.propTypes={children:i().node,className:i().string,color:d.TX.isRequired,dismissible:i().bool,onClose:i().func,variant:i().string,visible:i().bool},p.displayName="CAlert"},23793:(e,t,s)=>{s.d(t,{E:()=>o});var a=s(3035),n=s(9950),r=s(11942),i=s.n(r),c=s(69344),o=(0,n.forwardRef)((function(e,t){var s=e.className,r=e.dark,i=e.disabled,o=e.white,l=(0,a.Tt)(e,["className","dark","disabled","white"]);return n.createElement("button",(0,a.Cl)({type:"button",className:(0,c.A)("btn","btn-close",{"btn-close-white":o},i,s),"aria-label":"Close",disabled:i},r&&{"data-coreui-theme":"dark"},l,{ref:t}))}));o.propTypes={className:i().string,dark:i().bool,disabled:i().bool,white:i().bool},o.displayName="CCloseButton"},38870:(e,t,s)=>{s.r(t),s.d(t,{default:()=>m});var a=s(89379),n=s(9950),r=s(64771),i=s(30578),c=s(13019),o=s(98114),l=s(9134),d=s(61114),u=s(79894),p=s(44414);const m=()=>{const[e,t]=(0,n.useState)({}),[s,m]=(0,n.useState)(!1),[g,h]=(0,n.useState)(""),y=async e=>{m(!0),h("");try{const s=await u.X.get(e);console.log("Response from ".concat(e,":"),s),t((t=>(0,a.A)((0,a.A)({},t),{},{[e]:s})))}catch(s){console.error("Error from ".concat(e,":"),s),h("Failed to fetch ".concat(e))}m(!1)};return(0,n.useEffect)((()=>{y("/api/incident/categories"),y("/api/incident/types"),y("/api/incident/severities")}),[]),(0,p.jsx)(r.T,{children:(0,p.jsxs)(i.E,{children:[(0,p.jsx)(c.V,{children:(0,p.jsx)("h3",{children:"Incident API Test"})}),(0,p.jsxs)(o.W,{children:[g&&(0,p.jsx)(l.k,{color:"danger",children:g}),s&&(0,p.jsx)("p",{children:"Loading..."}),(0,p.jsx)("h4",{children:"API Responses:"}),(0,p.jsx)("pre",{style:{backgroundColor:"#f5f5f5",padding:"10px",borderRadius:"5px"},children:JSON.stringify(e,null,2)}),(0,p.jsxs)("div",{className:"mt-3",children:[(0,p.jsx)(d.Q,{color:"primary",onClick:()=>y("/api/incident/categories"),children:"Test Categories"})," ",(0,p.jsx)(d.Q,{color:"secondary",onClick:()=>y("/api/incident/types"),children:"Test Types"})," ",(0,p.jsx)(d.Q,{color:"info",onClick:()=>y("/api/incident/severities"),children:"Test Severities"})]})]})]})})}},79894:(e,t,s)=>{s.d(t,{X:()=>n});var a=s(82932);const n={async get(e){try{return{success:!0,data:(await a.Ay.get(e)).data}}catch(t){return console.error("API GET Error:",t),{success:!1,data:null,message:"Request failed"}}},async post(e,t,s){try{return{success:!0,data:(await a.Ay.post(e,t,s)).data}}catch(n){return console.error("API POST Error:",n),{success:!1,data:null,message:"Request failed"}}},async put(e,t){try{return{success:!0,data:(await a.Ay.put(e,t)).data}}catch(s){return console.error("API PUT Error:",s),{success:!1,data:null,message:"Request failed"}}},async delete(e){try{return{success:!0,data:(await a.Ay.delete(e)).data}}catch(t){return console.error("API DELETE Error:",t),{success:!1,data:null,message:"Request failed"}}}}},82932:(e,t,s)=>{s.d(t,{Ay:()=>i,n9:()=>c});var a=s(26910);const n={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_API_URL||"/api",r=a.A.create({baseURL:n,headers:{"Content-Type":"application/json"}});r.interceptors.request.use((e=>{const t=localStorage.getItem("token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e})),r.interceptors.response.use((e=>e),(e=>{var t;return 401===(null===(t=e.response)||void 0===t?void 0:t.status)&&(localStorage.removeItem("token"),window.location.href="/login"),Promise.reject(e)}));const i=r,c={getDashboardStats:()=>r.get("/dashboard/stats"),getIncidents:e=>r.get("/incidents",{params:e}),createIncident:e=>r.post("/incidents",e),getIncident:e=>r.get("/incidents/".concat(e)),updateIncident:(e,t)=>r.put("/incidents/".concat(e),t),deleteIncident:e=>r.delete("/incidents/".concat(e)),getRiskMatrix:()=>r.get("/risks/matrix"),getRiskAssessments:e=>r.get("/risks/assessments",{params:e}),createRiskAssessment:e=>r.post("/risks/assessments",e),getRiskAssessment:e=>r.get("/risks/assessments/".concat(e)),updateRiskAssessment:(e,t)=>r.put("/risks/assessments/".concat(e),t),getPermits:e=>r.get("/permits",{params:e}),getActivePermits:()=>r.get("/permits/active"),createPermit:e=>r.post("/permits",e),getPermit:e=>r.get("/permits/".concat(e)),updatePermit:(e,t)=>r.put("/permits/".concat(e),t),approvePermit:e=>r.post("/permits/".concat(e,"/approve")),closePermit:e=>r.post("/permits/".concat(e,"/close")),getTrainingRecords:e=>r.get("/training",{params:e}),getTrainingCompliance:()=>r.get("/training/compliance"),createTrainingRecord:e=>r.post("/training",e),getDocuments:e=>r.get("/documents",{params:e}),uploadDocument:e=>r.post("/documents/upload",e,{headers:{"Content-Type":"multipart/form-data"}}),downloadDocument:e=>r.get("/documents/".concat(e,"/download"),{responseType:"blob"}),getAnalytics:e=>r.get("/analytics",{params:e}),getIncidentTrends:()=>r.get("/analytics/incident-trends"),getRiskHeatmap:()=>r.get("/analytics/risk-heatmap"),getComplianceMetrics:()=>r.get("/analytics/compliance-metrics")}}}]);