{"name": "digitalhse-web", "version": "0.1.0", "private": true, "dependencies": {"@coreui/chartjs": "^3.1.2", "@coreui/coreui": "^5.4.0", "@coreui/icons": "^3.0.1", "@coreui/icons-react": "^2.3.0", "@coreui/react": "^5.7.0", "@coreui/react-chartjs": "^3.0.0", "@coreui/utils": "^2.0.2", "@hookform/resolvers": "^3.3.4", "@types/node": "^20.11.5", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "axios": "^1.6.5", "chart.js": "^4.4.1", "classnames": "^2.5.1", "core-js": "^3.35.0", "date-fns": "^3.2.0", "i18next": "^23.7.16", "i18next-browser-languagedetector": "^7.2.0", "i18next-http-backend": "^2.4.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.49.3", "react-i18next": "^14.0.0", "react-redux": "^9.1.0", "react-router-dom": "^6.21.3", "react-scripts": "^5.0.1", "react-toastify": "^10.0.4", "redux": "^5.0.1", "simplebar-react": "^3.2.4", "web-vitals": "^3.5.1", "yup": "^1.4.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "build:copy": "npm run build && cp -r build/* ../wwwroot/", "test": "react-scripts test", "eject": "react-scripts eject", "generate-types": "echo 'Type generation script will be added'"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/jest": "^29.5.11", "http-proxy-middleware": "^2.0.6", "sass": "^1.70.0", "typescript": "^5.8.3"}}